import { useEffect, useState } from "react";
import Head from "next/head";
import {
    Breadcrumb,
    IconButton,
    Input,
    InputGroup,
    Pagination,
    Panel,
    Stack,
    Table,
    Tag,
    Button,
    Modal,
    Form,
    Loader,
    RadioGroup,
    Radio,
    SelectPicker,
} from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import EditIcon from "@rsuite/icons/Edit";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiSiskoTransactionHeader from "@/pages/api/sisko/transaction_h/api_sisko_transaction_h"
import ApiMasterdata_ppr from "@/pages/api/sisko/masterdata_ppr/api_masterdata_ppr";
import { useUser } from "@/context/UserContext";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';

function OperatorCreationList() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState("");
    const [sortType, setSortType] = useState("asc");
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const router = useRouter();
    const [loading, setLoading] = useState(false);
    const { user, isAuthenticated, loading: userLoading } = useUser();

    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);

    const [transactionHeadersDataState, setTransactionHeadersDataState] = useState([]);
    const [ppiDataState, setPPIDataState] = useState([]);
    const [batchCodeDataState, setBatchCodeDataState] = useState([]);

    const emptyEditTransactionHeaderForm = {
        id_transaksi_header: null,
        id_ppr: null,
        kode_batch: "",
        catatan: "",
        wetmill: "N",
        status_transaksi: 2,
        tanggal_dibuat: null,
        dibuat_oleh: "",
        tanggal_diubah: null,
        diubah_oleh: null,
        tanggal_dihapus: null,
        dihapus_oleh: null,
    };

    const [showEditModal, setShowEditModal] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [showRemarksModal, setShowRemarksModal] = useState(false);
    const [showDetailWerumModal, setShowDetailWerumModal] = useState(false);
    const [editTransactionHeaderForm, setEditTransactionHeaderForm] = useState(emptyEditTransactionHeaderForm);
    const [werumDataState, setWerumDataState] = useState([])
    const [errorsEditForm, setErrorsEditForm] = useState({});

    const [selectedRemarks, setSelectedRemarks] = useState("");
    const [selectedTransactionId, setSelectedTransactionId] = useState(null);

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = transactionHeadersDataState.filter((rowData, i) => {
        const searchFields = ["id_transaksi_header", "nama_ppr", "kode_batch", "line_desc", "catatan", "wetmill", "status_transaksi", "tanggal_dibuat", "dibuat_oleh", "tanggal_diubah", "diubah_oleh", "tanggal_dihapus", "dihapus_oleh"];

        const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : transactionHeadersDataState.length;

    const HandleGetAllTransactionHeaderApi = async () => {
        try {
            const res = await ApiSiskoTransactionHeader().getAllActiveSiskoTransactionHeader();

            console.log("res", res);
            if (res.status === 200) {
                setTransactionHeadersDataState(res.data || []);
            } else {
                console.log("gagal mendapatkan data transaksi ", res.message);
            }
        } catch (error) {
            console.log("gagal mendapatkan data transaksi", error);
        }
    };

    const HandleGetAllPPIApi = async () => {
        try {
            const res = await ApiMasterdata_ppr().getAllFullyApproveMasterPPR();
            if (res.status === 200) {
                const options = res.data.map((ppi) => ({
                    label: ppi.nama_ppr,
                    value: ppi.id_ppr,
                    wetmill: ppi.wetmill,
                }));
                setPPIDataState(options);
            } else {
                console.log("Gagal Mengambil data PPI", res.message);
            }
        } catch (error) {
            console.log("Gagal Mengambil data PPI", error);
        }
    };



    const HandleEditTransactionHeaderApi = async () => {
        const errors = {};

        // Cek apakah kombinasi kode_batch dan id_ppr sudah ada pada transaksi selain yang sedang diedit
        const isDuplicate = transactionHeadersDataState.some(
            (transaction) => transaction.kode_batch === editTransactionHeaderForm.kode_batch && transaction.id_ppr === editTransactionHeaderForm.id_ppr && transaction.id_transaksi_header !== editTransactionHeaderForm.id_transaksi_header
        );

        if (isDuplicate) {
            errors.kode_batch = "Kode Batch Dan PPR Sudah Ada";
        }

        if (!editTransactionHeaderForm.id_ppr) {
            errors.id_ppr = "Nama PPR Wajib Diisi";
        }
        if (!editTransactionHeaderForm.kode_batch) {
            errors.kode_batch = "Kode Batch Wajib Diisi";
        }

        if (!editTransactionHeaderForm.catatan) {
            errors.catatan = "catatan Wajib Diisi";
        }

        // If there are any errors, set them in the state
        if (Object.keys(errors).length > 0) {
            setErrorsEditForm(errors);
            return;
        }
        try {
            const res = await ApiSiskoTransactionHeader().editSiskoTransactionHeader({
                ...editTransactionHeaderForm,
                diubah_oleh: sessionAuth.no_karyawan + " - " + sessionAuth.username,
            });

            if (res.status === 200) {
                HandleGetAllTransactionHeaderApi();
                setShowEditModal(false);
            } else {
                console.log("gagal mengubah data", res.message);
            }
        } catch (error) {
            console.log("error gagal mengubah data ", error);
        }
    };

    const HandleEditStatusTransactionHeaderApi = async (id_transaksi_header, status) => {
        try {
            const res = await ApiSiskoTransactionHeader().editSiskoStatusTransactionHeader({
                id_transaksi_header,
                status,
                dihapus_oleh: sessionAuth.no_karyawan + " - " + sessionAuth.username,
            });

            if (res.status === 200) {
                console.log("update status berhasil", res.message);
                HandleGetAllTransactionHeaderApi();
            } else {
                console.log("gagal update status ", res.message);
            }
        } catch (error) {
            console.log("Error gagal update status ", error);
        }
    };

    const handleShowConfirmModal = (id) => {
        setSelectedTransactionId(id);
        setShowConfirmModal(true);
    };

    // --- Useeffect sesuai dengan project (menggunakan context user, isAuthenticated, userLoading) ---
    useEffect(() => {
        if (userLoading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        setModuleName(user?.module_name || "");
        setSessionAuth(user);
        HandleGetAllTransactionHeaderApi();
        HandleGetAllPPIApi();

    }, [user, userLoading]);
    // --- End useeffect sesuai project ---

    return (
        <div>
            <div>
                <Head>
                    <title>Sisko Transaction Header</title>
                </Head>
            </div>
            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>Sisko</Breadcrumb.Item>
                                    <Breadcrumb.Item>Creation</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Sisko Pembuatan Header</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Pengajuan Transaksi</h5>
                            </Stack>
                        }
                    ></Panel>
                    <div>
                        <Panel
                            bordered
                            bodyFill
                            header={
                                <Stack justifyContent="space-between">
                                    <div className="flex gap-2">
                                        <IconButton
                                            icon={<PlusRoundIcon />}
                                            appearance="primary"
                                            onClick={() => {
                                                // setShowAddModal(true);
                                                router.push(`/sisko/operator/creation/addTransactionHeader`);
                                            }}
                                        >
                                            Tambah
                                        </IconButton>
                                    </div>
                                    <InputGroup inside>
                                        <InputGroup.Addon>
                                            <SearchIcon />
                                        </InputGroup.Addon>
                                        <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                                        <InputGroup.Addon
                                            onClick={() => {
                                                setSearchKeyword("");
                                                setPage(1);
                                            }}
                                            style={{
                                                display: searchKeyword ? "block" : "none",
                                                color: "red",
                                                cursor: "pointer",
                                            }}
                                        >
                                            <CloseOutlineIcon />
                                        </InputGroup.Addon>
                                    </InputGroup>
                                </Stack>
                            }
                        >
                            <Table
                                bordered
                                cellBordered
                                height={400}
                                data={getPaginatedData(getFilteredData(), limit, page)}
                                sortColumn={sortColumn}
                                sortType={sortType}
                                onSortColumn={handleSortColumn}

                            >
                                <Column width={70} align='center' fixed>
                                    <HeaderCell>No</HeaderCell>
                                    <Cell>
                                        {(rowData, index) => {
                                            return index + 1 + limit * (page - 1);
                                        }}
                                    </Cell>
                                </Column>
                                <Column width={70} align="center" sortable fullText resizable>
                                    <HeaderCell>ID Transaksi Header</HeaderCell>
                                    <Cell dataKey="id_transaksi_header" />
                                </Column>
                                <Column width={180} sortable fullText resizable>
                                    <HeaderCell align="center">Nama PPR</HeaderCell>
                                    <Cell dataKey="nama_ppr" />
                                </Column>
                                <Column width={180} sortable fullText resizable>
                                    <HeaderCell align="center">Kode Batch</HeaderCell>
                                    <Cell dataKey="kode_batch" />
                                </Column>

                                <Column width={250} sortable fullText resizable>
                                    <HeaderCell align="center">Catatan</HeaderCell>
                                    <Cell dataKey="catatan" />
                                </Column>

                                <Column width={250} align="center" sortable fullText resizable>
                                    <HeaderCell>Status Transaksi</HeaderCell>
                                    <Cell>
                                        {(rowData) => {
                                            let statusText = "";
                                            if (rowData.status_transaksi === 2) {
                                                statusText = "Draft";
                                            } else if (rowData.status_transaksi === 1) {
                                                statusText = "Done";
                                            } else if (rowData.status_transaksi === 0) {
                                                statusText = "Dropped";
                                            }
                                            return <>{statusText}</>;
                                        }}
                                    </Cell>
                                </Column>
                                <Column width={175} sortable resizable align="center" fullText >
                                    <HeaderCell>Tanggal Dibuat</HeaderCell>
                                    <Cell>{(rowData) => new Date(rowData.tanggal_dibuat).toLocaleDateString("en-GB")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.dibuat_oleh}</>}</Cell>
                                </Column>
                                <Column width={175} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Diperbarui</HeaderCell>
                                    <Cell>{(rowData) => (rowData.tanggal_diubah ? new Date(rowData.tanggal_diubah).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.diubah_oleh}</>}</Cell>
                                </Column>
                                <Column width={175} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Dihapus</HeaderCell>
                                    <Cell>{(rowData) => (rowData.tanggal_dihapus ? new Date(rowData.tanggal_dihapus).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.dihapus_oleh}</>}</Cell>
                                </Column>
                                <Column width={120} sortable resizable align="center" fullText>
                                    <HeaderCell>Status</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <span
                                                style={{
                                                    color: rowData.status === 1 ? "green" : "red",
                                                }}
                                            >
                                                {rowData.status === 1 ? "Aktif" : "Tidak Aktif"}
                                            </span>
                                        )}
                                    </Cell>
                                </Column>
                                <Column width={100} fixed="right" align="center">
                                    <HeaderCell>Status Transaksi</HeaderCell>
                                    <Cell>
                                        {(rowData) => {
                                            let statusText = "";
                                            if (rowData.status_transaksi === 2) {
                                                statusText = "Draft";
                                            } else if (rowData.status_transaksi === 1) {
                                                statusText = "Done";
                                            } else if (rowData.status_transaksi === 0) {
                                                statusText = "Dropped";
                                            }
                                            return <>{statusText}</>;
                                        }}
                                    </Cell>
                                </Column>
                                <Column width={100} fixed="right" align="center">
                                    <HeaderCell>Status Approval</HeaderCell>
                                    <Cell style={{ padding: "0", margin: "0", display: "flex", alignItems: "center" }}>
                                        {(rowData) => {
                                            let statusText = "";
                                            if (rowData.status_persetujuan === 2) {
                                                // Jika status_transaksi juga 1 (Done), tampilkan "Waiting List"
                                                if (rowData.status_transaksi === 1) {
                                                    statusText = "Waiting List";
                                                } else {
                                                    statusText = "-";
                                                }
                                            } else if (rowData.status_persetujuan === 1) {
                                                statusText = "Approve";
                                            } else if (rowData.status_persetujuan === 0) {
                                                return (
                                                    <Button
                                                        appearance="ghost"
                                                        color="red"
                                                        size="sm"
                                                        onClick={() => {
                                                            setShowRemarksModal(true);
                                                            setSelectedRemarks(rowData.catatan_revisi);
                                                        }}
                                                    >
                                                        Revisi
                                                    </Button>
                                                );
                                            }
                                            return <span>{statusText}</span>;
                                        }}
                                    </Cell>
                                </Column>

                                <Column width={100} fixed="right" align="center">
                                    <HeaderCell>Aksi</HeaderCell>
                                    <Cell style={{ padding: "8px" }}>
                                        {(rowData) => {
                                            // Kondisi untuk disable semua tombol
                                            const isAllDisabled = rowData.status === 0;

                                            // Kondisi khusus untuk status revisi
                                            const isRevisionStatus = rowData.status_persetujuan === 0;

                                            // Kondisi untuk tombol Search/Plus
                                            const isSearchEnabled = rowData.status_transaksi === 1 && rowData.status_persetujuan === 1;

                                            // Kondisi untuk tombol Edit - disable jika status revisi
                                            const isEditDisabled = rowData.status === 0 || rowData.status_persetujuan === 1 || isSearchEnabled || isRevisionStatus;

                                            // Kondisi untuk tombol Trash - hanya aktif jika status revisi
                                            const isTrashDisabled = rowData.status === 0 || rowData.status_persetujuan !== 0;

                                            return (
                                                <div>
                                                    {/* Tombol Search/Plus */}
                                                    <Button
                                                        appearance="subtle"
                                                        size="sm"
                                                        disabled={isAllDisabled || isRevisionStatus}
                                                        onClick={() => {
                                                            if (rowData.status_transaksi === 1) {
                                                                // Jika status Done, arahkan ke halaman list dengan ID
                                                                router.push(`/sisko/operator/creation/list?id=${rowData.id_transaksi_header}`);
                                                            } else {
                                                                // Jika status Draft, arahkan ke halaman detail
                                                                router.push(`/sisko/operator/creation/detail/${rowData.id_transaksi_header}`);
                                                            }
                                                        }}
                                                        title={isAllDisabled ? "Transaksi tidak aktif" :
                                                            isRevisionStatus ? "Tidak dapat melihat detail transaksi yang perlu direvisi" :
                                                                (rowData.status_transaksi === 1 ? "Lihat Detail" : "Edit Detail")}
                                                    >
                                                        {rowData.status_transaksi === 1 ? (
                                                            <SearchIcon />
                                                        ) : (
                                                            <PlusRoundIcon />
                                                        )}
                                                    </Button>

                                                    {/* Tombol Edit */}
                                                    <Button
                                                        appearance="subtle"
                                                        disabled={isEditDisabled}
                                                        onClick={() => {
                                                            setShowEditModal(true);
                                                            setEditTransactionHeaderForm({
                                                                ...editTransactionHeaderForm,
                                                                id_ppr: rowData.id_ppr,
                                                                kode_batch: rowData.kode_batch,
                                                                line_desc: rowData.line_desc,
                                                                catatan: rowData.catatan,
                                                                id_transaksi_header: rowData.id_transaksi_header,
                                                                wetmill: rowData.wetmill,
                                                                diubah_oleh: sessionAuth.no_karyawan + " - " + sessionAuth.username,
                                                            });
                                                        }}
                                                        title={isEditDisabled ?
                                                            (rowData.status === 0 ? "Tidak dapat mengedit transaksi yang dinonaktifkan" :
                                                                (isRevisionStatus ? "Tidak dapat mengedit transaksi yang perlu direvisi" :
                                                                    (isSearchEnabled ? "Transaksi sudah selesai dan disetujui" : "Tidak dapat mengedit transaksi yang sudah disetujui")))
                                                            : "Edit Transaksi"}
                                                    >
                                                        <EditIcon />
                                                    </Button>

                                                    {/* Tombol Trash */}
                                                    <Button
                                                        appearance="subtle"
                                                        onClick={() => handleShowConfirmModal(rowData.id_transaksi_header)}
                                                        disabled={isTrashDisabled}
                                                        title={isTrashDisabled ?
                                                            (rowData.status === 0 ? "Transaksi tidak aktif" :
                                                                (isSearchEnabled ? "Transaksi sudah selesai dan disetujui" : "Hanya transaksi dengan status approval 'Revisi' yang bisa dihapus"))
                                                            : "Hapus Transaksi"}
                                                    >
                                                        <TrashIcon style={{ fontSize: "16px" }} />
                                                    </Button>
                                                </div>
                                            );
                                        }}
                                    </Cell>
                                </Column>

                            </Table>

                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                                    limitOptions={[10, 30, 50]}
                                    total={totalRowCount}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>
                        <Modal
                            backdrop="static"
                            open={showEditModal}
                            onClose={() => {
                                setShowEditModal(false);
                                setEditTransactionHeaderForm(emptyEditTransactionHeaderForm);
                                setErrorsEditForm({});
                            }}
                            overflow={false}
                        >
                            <Modal.Header>
                                <Modal.Title>Ubah Transaksi Header</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Form fluid>
                                    <Form.Group>
                                        <Form.ControlLabel>Nama PPR</Form.ControlLabel>
                                        <SelectPicker
                                            data={ppiDataState} // Menggunakan data PPI yang telah di-fetch
                                            value={editTransactionHeaderForm.id_ppr} // Nilai yang dipilih untuk edit
                                            onChange={(value) => {
                                                setEditTransactionHeaderForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    id_ppr: value,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    id_ppr: undefined,
                                                }));
                                            }}
                                            block
                                            placeholder="Select PPR"
                                            style={{ width: "100%" }}
                                        />
                                        {errorsEditForm.id_ppr && <p style={{ color: "red" }}>{errorsEditForm.id_ppr}</p>}
                                    </Form.Group>
                                    <Form.Group>
                                        <Form.ControlLabel>Kode Batch</Form.ControlLabel>
                                        <Form.Control
                                            name="kode_batch"
                                            value={editTransactionHeaderForm.kode_batch}
                                            onChange={(value) => {
                                                setEditTransactionHeaderForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    kode_batch: value,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    kode_batch: undefined,
                                                }));
                                            }}
                                            className="w-full"
                                        />
                                        {errorsEditForm.kode_batch && <p style={{ color: "red" }}>{errorsEditForm.kode_batch}</p>}
                                    </Form.Group>


                                    <Form.Group>
                                        <Form.ControlLabel>Catatan</Form.ControlLabel>
                                        <Form.Control
                                            name="catatan"
                                            value={editTransactionHeaderForm.catatan}
                                            onChange={(value) => {
                                                setEditTransactionHeaderForm((prevFormValue) => ({
                                                    ...prevFormValue,
                                                    catatan: value,
                                                }));
                                                setErrorsEditForm((prevErrors) => ({
                                                    ...prevErrors,
                                                    catatan: undefined,
                                                }));
                                            }}
                                        />
                                        {errorsEditForm.catatan && <p style={{ color: "red" }}>{errorsEditForm.catatan}</p>}
                                    </Form.Group>

                                </Form>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    onClick={() => {
                                        setShowEditModal(false);
                                        setEditTransactionHeaderForm(emptyEditTransactionHeaderForm);
                                        setErrorsEditForm({});
                                    }}
                                    appearance="subtle"
                                >
                                    Batal
                                </Button>
                                <Button
                                    onClick={() => {
                                        HandleEditTransactionHeaderApi();
                                    }}
                                    appearance="primary"
                                    type="submit"
                                >
                                    Simpan
                                </Button>
                                {loading && <Loader backdrop size="md" vertical content="Editing Data..." active={loading} />}
                            </Modal.Footer>
                        </Modal>

                        <Modal
                            open={showConfirmModal}
                            onClose={() => {
                                setShowConfirmModal(false);
                            }}
                            backdrop="static">
                            <Modal.Header>
                                <Modal.Title>Konfirmasi Penghapusan</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                Apakah Anda yakin ingin menghapus transaksi ini?
                            </Modal.Body>
                            <Modal.Footer>
                                <Button onClick={() => {
                                    setShowConfirmModal(false);
                                }}
                                    appearance="subtle">
                                    Batal
                                </Button>
                                <Button onClick={() => {
                                    HandleEditStatusTransactionHeaderApi(selectedTransactionId, 1);
                                    setShowConfirmModal(false);
                                }}
                                    appearance="primary" color="red">
                                    Ya, Hapus
                                </Button>
                            </Modal.Footer>
                        </Modal>
                        <Modal
                            open={showRemarksModal}
                            onClose={() => setShowRemarksModal(false)}
                            size="sm"
                        >
                            <Modal.Header>
                                <Modal.Title>Catatan Revisi</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                {selectedRemarks}
                            </Modal.Body>
                            <Modal.Footer>
                                <Button onClick={() => setShowRemarksModal(false)} appearance="subtle">
                                    Tutup
                                </Button>
                            </Modal.Footer>
                        </Modal>
                    </div>
                </div>
            </ContainerLayout>
        </div>
    );
}

export default withRoleAccess(OperatorCreationList, rolePermissions['sisko/operator/creation']);
