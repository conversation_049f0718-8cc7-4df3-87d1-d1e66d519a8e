// pages/404.js

import { useEffect } from "react";
import { useRouter } from "next/router";
import { Button } from "rsuite";
import Head from "next/head";

const Custom404 = () => {
    const router = useRouter();

    useEffect(() => {
        const dataLogin = JSON.parse(localStorage.getItem("user"));
        if (!dataLogin) {
            router.push("/");
            return;
        }
    }, []);

    return (
        <>
            <Head>
                <title>404 Page NOT FOUND</title>
            </Head>
            <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
                <div className="w-full max-w-md">
                    <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                        <div className="mb-6">
                            <img
                                src="/Logo_kalbe_detail.png"
                                alt="Kalbe Logo"
                                className="w-48 mx-auto"
                            />
                        </div>
                        <div className="mb-6">
                            <h2 className="text-2xl font-bold text-gray-800 mb-3">
                                404 Page NOT FOUND
                            </h2>
                            <p className="text-gray-600">
                                The page you are looking for doesn't exist.
                            </p>
                        </div>
                        <Button
                            appearance="primary"
                            block
                            onClick={() => router.push("/dashboard")}
                        >
                            Back to Dashboard
                        </Button>
                    </div>
                </div>
            </div>
        </>
    );
};

export default Custom404;