/**
 * Fungsi untuk memformat tanggal dari format database (YYYY-MM-DD HH:mm:ss.SSS)
 * menjadi format yang lebih mudah dibaca (DD/MM/YYYY)
 * @param {string} dateString - String tanggal dari database
 * @returns {string} - String tanggal yang sudah diformat
 */
export const formatDate = (dateString) => {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    
    // Menyesuaikan zona waktu dengan mengurangi 7 jam
    date.setHours(date.getHours() - 7);
    
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
  } catch (error) {
    console.error('Error formatting date:', error);
    return '-';
  }
};