// pages/404.js

import { useEffect } from "react";
import { useRouter } from "next/router";
import { Button } from "rsuite";
import Head from "next/head";

const NoMenuFound = () => {
    const router = useRouter();

    // comment
    useEffect(() => {
        const dataLogin = JSON.parse(localStorage.getItem("user"));
        if (!dataLogin) {
            router.push("/");
            return;
        }
    });

    return (
        <>
            <Head>
                <title>No Menu FOUND.</title>
            </Head>
            <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 mb-5">
                <div className="w-full max-w-md mt-5">
                    <div className="bg-white rounded-lg shadow-lg p-8 text-center">
                        <div className="mb-5">
                            <img
                                src="/Logo_kalbe_detail.png"
                                alt="Kalbe Logo"
                                className="w-48 mx-auto block"
                            />
                        </div>
                        <div className="mb-2">
                            <h4 className="text-xl font-semibold text-gray-800">
                                No Menu FOUND.
                            </h4>
                        </div>
                        <Button
                            appearance="primary"
                            onClick={() => router.push("/dashboard")}
                        >
                            Back to Dashboard
                        </Button>
                    </div>
                </div>
            </div>
        </>
    );
};

export default NoMenuFound;