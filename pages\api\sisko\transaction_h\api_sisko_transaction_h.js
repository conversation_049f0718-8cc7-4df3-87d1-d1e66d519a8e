import createApiFunction from "@/lib/apiClient";

export default function ApiSiskoTransactionHeader() {
  return {
    getAllSiskoTransactionHeader: createApiFunction("get", "sisko/transaction_h/list"),
    getAllActiveSiskoTransactionHeader : createApiFunction("get", "sisko/transaction_h/list-active"),
    getAllNeedApproveSiskoTransactionHeader: createApiFunction("get", "sisko/transaction_h/need-approve"),
    getAllNeedApproveSiskoTransactionHeaderBeratMsc: createApiFunction("get", "sisko/berat_msc/need-approve"),
    getAllFullyApproveSiskoTransactionHeader: createApiFunction("get", "sisko/transaction_h/fully-approve"),
    getReadyCpandCpkTransactionSiskoHeader: createApiFunction("get", "sisko/transaction_h/ready-cp-cpk"),
    createSiskoTransactionHeader: createApiFunction("post", "sisko/transaction_h/create"),
    editSiskoTransactionHeader: createApiFunction("put", "sisko/transaction_h/edit"),
    editSiskoStatusTransactionHeader: createApiFunction("put", "sisko/transaction_h/edit-status"),
    getPPRSiskoTransactionHeaderById: createApiFunction("post", "sisko/transaction_h/ppr_byIdHeader"),
    editSiskoStatusTransactionHeaderDone: createApiFunction("put", "sisko/transaction_h/edit-status-done"),
    editSiskoStatusApprovalTransactionHeader: createApiFunction("put", "sisko/transaction_h/edit-status-approval"),
    editSiskoStatusApprovalTransactionHeaderBeratMsc: createApiFunction("put", "sisko/berat_msc/edit-status-approve"),
  };
}
