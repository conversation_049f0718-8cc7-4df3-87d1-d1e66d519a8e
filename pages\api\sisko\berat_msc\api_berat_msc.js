import createApiFunction from "@/lib/apiClient";

export default function ApiBeratMSC() {
  return {
    getAllBeratMSC: createApiFunction("get", "sisko/berat_msc/list"),
    getMSCbyIdTransHeader: createApiFunction("post", "sisko/berat_msc/id"),
    createBeratMSC: createApiFunction("post", "sisko/berat_msc/create"),
    editBeratMSC: createApiFunction("put", "sisko/berat_msc/edit"),
    getBeratMscByIdPpr: createApiFunction("post", "sisko/cp_and_cpk/id-ppr"),
  };
}
