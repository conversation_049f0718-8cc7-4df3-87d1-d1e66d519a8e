import { useEffect, useState, useCallback } from "react";
import Head from "next/head";
import { Breadcrumb, Stack, Panel, Tag, Form, Grid, Row, Col, Table, Button, Pagination, Modal, Loader, IconButton, InputGroup, Input, InputNumber, Checkbox, SelectPicker, useToaster, Notification } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import ApiSiskoTransactionHeader from "@/pages/api/sisko/transaction_h/api_sisko_transaction_h";
import ApiMasterdataKodeProduk from "@/pages/api/sisko/masterdata_kode_produk/api_masterdata_kode_produk";
import ApiMasterdata_ppr from "@/pages/api/sisko/masterdata_ppr/api_masterdata_ppr";
import ApiBeratMSC from "@/pages/api/sisko/berat_msc/api_berat_msc";
import { useUser } from "@/context/UserContext";

export default function MassaSiapCetakPage() {
    const router = useRouter();
    const { IdHeader } = router.query;
    const toaster = useToaster();
    const { user, isAuthenticated, loading: userLoading } = useUser();

    const [searchKeyword, setSearchKeyword] = useState("");
    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [idRouter, setIdRouter] = useState(null);
    const [isEditMode, setIsEditMode] = useState(false);
    const [isViewMode, setIsViewMode] = useState(false);

    const emptyAddTransactionMscForm = {
        id_transaksi_header: null,
        berat_msc: null,
        percentage_berat_msc: null,
        catatan: null,
        bobot_min: null,
        bobot_max: null,
        bobot_std: null,
        dibuat_oleh: sessionAuth ? sessionAuth.employee_name : "",
    };

    const [transactionDetailsDataState, setTransactionDetailDataState] = useState([]);
    const [ppiData, setPpiData] = useState(null);
    const [productData, setProductData] = useState(null);
    const [formDataHeader, setformDataHeader] = useState({});
    const [addTransactionMscForm, setAddTransactionMscForm] = useState(emptyAddTransactionMscForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [addLoading, setAddLoading] = useState(false);
    const [loading, setLoading] = useState(false);

    const HandleGetDetailTransactionHeader = async (id_transaksi_header) => {
        try {
            const apiTransactionHeader = ApiSiskoTransactionHeader();
            const response = await apiTransactionHeader.getPPRSiskoTransactionHeaderById({ id_transaksi_header: parseInt(id_transaksi_header) });
            if (response.status === 200) {
                const data = response.data;
                setformDataHeader({
                    id_transaksi_header: data.id_transaksi_header,
                    id_ppr: data.id_ppr,
                    nama_ppr: data.nama_ppr,
                    kode_batch: data.kode_batch,
                    iot_desc: data.iot_desc,
                    line_desc: data.line_desc,
                    catatan: data.catatan,
                    wetmill: data.wetmill,
                    status_transaksi: data.status_transaksi,
                    tanggal_dibuat: data.tanggal_dibuat ? new Date(data.tanggal_dibuat).toLocaleDateString("en-GB") : "-",
                    dibuat_oleh: data.dibuat_oleh || "-",
                    tanggal_diubah: data.tanggal_diubah ? new Date(data.tanggal_diubah).toLocaleDateString("en-GB") : "-",
                    diubah_oleh: data.diubah_oleh || "-",
                    tanggal_dihapus: data.tanggal_dihapus ? new Date(data.tanggal_dihapus).toLocaleDateString("en-GB") : "-",
                    dihapus_oleh: data.dihapus_oleh || "-",
                });

                if (data.id_ppr) {
                    const ppiResponse = await HandleGetPPIById(data.id_ppr);

                    if (ppiResponse) {
                        setPpiData(ppiResponse);
                        if (ppiResponse.id_produk) {
                            const productResponse = await HandleGetProductById(ppiResponse.id_produk);
                            if (productResponse) {
                                setProductData(productResponse);
                            }
                        }
                    }
                }

                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetPPIById = async (id_ppr) => {
        try {
            const api = ApiMasterdata_ppr();
            const response = await api.GetMasterPPRById({ id_ppr: parseInt(id_ppr) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetProductById = async (id_produk) => {
        try {
            const api = ApiMasterdataKodeProduk();
            const response = await api.getMasterKodeProdukById({ id_produk: parseInt(id_produk) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetMSCWeightByIdTransHeader = async (id_transaksi_header) => {
        try {
            const api = ApiBeratMSC();
            const response = await api.getMSCbyIdTransHeader({ id_transaksi_header: parseInt(id_transaksi_header) });

            if (response.status === 200) {
                const data = response.data;
                if (data) {
                    setAddTransactionMscForm({
                        id_transaksi_header: data.id_transaksi_header,
                        id_berat_msc: data.id_berat_msc,
                        berat_msc: data.berat_msc,
                        percentage_berat_msc: data.percentage_berat_msc,
                        catatan: data.catatan,
                        bobot_min: data.bobot_min,
                        bobot_max: data.bobot_max,
                        bobot_std: data.bobot_std,
                        dibuat_oleh: sessionAuth ? sessionAuth.employee_name : "",
                    });
                }
                return data;
            } else {
                console.log("gagal mendapatkan data detail", response.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const handleSubmit = async () => {
        const errors = {};

        if (!addTransactionMscForm.berat_msc) {
            errors.berat_msc = "Berat MSC wajib diisi";
        }

        if (!addTransactionMscForm.percentage_berat_msc) {
            errors.percentage_berat_msc = "Persentase Berat MSC wajib diisi";
        }

        if (!addTransactionMscForm.catatan) {
            errors.catatan = "catatan wajib diisi";
        }

        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }

        if (loading) return;

        setLoading(true);
        try {
            let res;

            if (isEditMode) {

                const updatePayload = {
                    ...addTransactionMscForm,
                    id_transaksi_header: parseInt(IdHeader, 10),
                    id_berat_msc: addTransactionMscForm.id_berat_msc,
                    bobot_min: parseFloat(addTransactionMscForm.bobot_min),
                    bobot_max: parseFloat(addTransactionMscForm.bobot_max),
                    bobot_std: parseFloat(addTransactionMscForm.bobot_std),
                    diubah_oleh: user.no_karyawan + " - " + user.username,
                };

                // Tampilkan payload di console
                console.log("FORM PAYLOAD (UPDATE):", updatePayload);

                res = await ApiBeratMSC().editBeratMSC(updatePayload);

            } else {

                const createPayload = {
                    ...addTransactionMscForm,
                    id_transaksi_header: parseInt(IdHeader, 10),
                    bobot_min: parseFloat(addTransactionMscForm.bobot_min),
                    bobot_max: parseFloat(addTransactionMscForm.bobot_max),
                    bobot_std: parseFloat(addTransactionMscForm.bobot_std),
                    dibuat_oleh: user.no_karyawan + " - " + user.username,
                };


                console.log("FORM PAYLOAD (CREATE):", createPayload);

                res = await ApiBeratMSC().createBeratMSC(createPayload);
            }

            if (res.status === 200) {
                showNotification("success", isEditMode ? "Data Berhasil Diperbarui" : "Data Berhasil Ditambahkan");
                router.push(`/sisko/operator/finishing`);
                setAddTransactionMscForm(emptyAddTransactionMscForm);
            } else {
                console.log("gagal " + (isEditMode ? "memperbarui" : "menambah") + " data", res.message);
                showNotification("error", isEditMode ? "Gagal Memperbarui data" : "Gagal Menambah data");
            }
        }
        catch (error) {
            console.log("error gagal " + (isEditMode ? "memperbarui" : "menambah") + " data ", error);
            showNotification("error", "Terjadi Kesalahan Saat " + (isEditMode ? "Memperbarui" : "Menambahkan") + " Data");
        }
        finally {
            setLoading(false);
        }
    };


    const totalRowCount = searchKeyword ? filteredData.length : transactionDetailsDataState.length;

    // --- Useeffect sesuai dengan project (menggunakan context user, isAuthenticated, userLoading) ---
    useEffect(() => {
        if (userLoading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        setModuleName(user?.module_name || "");
        setSessionAuth(user);

        if (IdHeader) {
            // Validasi akses menu
            // const validateUserAccess = user?.menu_link_code?.filter((item) => item.includes("sisko/operator"));
            // if (!validateUserAccess || validateUserAccess.length === 0) {
            //     router.push("/dashboard");
            //     return;
            // }

            console.log("Router data:", IdHeader);
            setIdRouter(IdHeader);

            HandleGetDetailTransactionHeader(IdHeader);

            const { mode } = router.query;
            if (mode === 'edit') {
                setIsEditMode(true);
                setIsViewMode(false);
                HandleGetMSCWeightByIdTransHeader(IdHeader);
            } else if (mode === 'view') {
                setIsEditMode(false);
                setIsViewMode(true);
                HandleGetMSCWeightByIdTransHeader(IdHeader);
            } else {
                setIsEditMode(false);
                setIsViewMode(false);
            }
        }
    }, [IdHeader, user, userLoading]);
    // --- End useeffect sesuai project ---

    useEffect(() => {
        if (productData) {
            setAddTransactionMscForm((prev) => ({
                ...prev,
                bobot_min: parseFloat(productData.bobot_min),
                bobot_max: parseFloat(productData.bobot_max),
                bobot_std: parseFloat(productData.bobot_std),
            }));
        }
    }, [productData]);

    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Transaksi Massa Siap Cetak</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>PQR</Breadcrumb.Item>
                                    <Breadcrumb.Item>List</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Transaction Header</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <Form layout="vertical">
                                    <Grid fluid>
                                        <Row style={{ marginBottom: "16px" }}>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                                                    <Form.Control name="id_transaksi_header" value={formDataHeader.id_transaksi_header} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                                                    <Form.Control name="nama_ppr" value={formDataHeader.nama_ppr} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                                                    <Form.Control name="kode_batch" value={formDataHeader.kode_batch} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "10px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                                                    <Form.Control name="wetmill" value={formDataHeader.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}></Row>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>catatan</Form.ControlLabel>
                                                        <Form.Control name="catatan" value={formDataHeader.catatan} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                                                        <Form.Control name="status_transaksi" value={formDataHeader.status_transaksi === 2 ? "Draft" : formDataHeader.status_transaksi === 1 ? "Done" : "Dropped"} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                                                        <Form.Control name="kode_produk" value={productData?.kode_produk ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}><Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Bobot Minimal</Form.ControlLabel>
                                                    <Form.Control name="bobot_min" value={productData?.bobot_min ?? '-'} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Maximal</Form.ControlLabel>
                                                        <Form.Control name="bobot_max" value={productData?.bobot_max ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Standar</Form.ControlLabel>
                                                        <Form.Control name="bobot_std" value={productData?.bobot_std ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                                        <Form.Control name="bobot_core_foil" value={productData?.bobot_core_foil ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}></Row>
                                            <Row style={{ marginBottom: "24px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                                                        <Form.Control name="tanggal_dibuat" value={formDataHeader.tanggal_dibuat} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                                                        <Form.Control name="created_by" value={formDataHeader.dibuat_oleh} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                                                        <Form.Control name="tanggal_diubah" value={formDataHeader.tanggal_diubah} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                                                        <Form.Control name="diubah_oleh" value={formDataHeader.diubah_oleh} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                                                    <Form.Control name="tanggal_dihapus" value={formDataHeader.tanggal_dihapus} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                                                        <Form.Control name="dihapus_oleh" value={formDataHeader.dihapus_oleh} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </Row>
                                    </Grid>
                                </Form>
                            </Stack>
                        }
                    />
                    <Panel
                        bordered
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Transaksi Massa Siap Cetak</h5>
                            </Stack>
                        }
                    >
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>Berat Massa Siap Cetak (MSC)</Form.ControlLabel>
                                <InputGroup>
                                    <Form.Control
                                        name="berat_msc"
                                        type="number"
                                        min={0}
                                        placeholder="Massa(Kg)"
                                        value={addTransactionMscForm.berat_msc}
                                        onChange={(value) => {
                                            const beratMsc = parseFloat(value);
                                            let percent = '';

                                            if (
                                                !isNaN(beratMsc) &&
                                                productData &&
                                                !isNaN(parseFloat(productData.bobot_std)) &&
                                                parseFloat(productData.bobot_std) !== 0
                                            ) {
                                                percent = ((beratMsc * 100) / parseFloat(productData.bobot_std)).toFixed(2);
                                            }

                                            setAddTransactionMscForm((prev) => ({
                                                ...prev,
                                                berat_msc: parseFloat(value),
                                                percentage_berat_msc: parseFloat(percent),
                                            }));

                                            setErrorsAddForm((prev) => ({
                                                ...prev,
                                                berat_msc: undefined,
                                            }));
                                        }}
                                        readOnly={isViewMode}

                                    />
                                    <InputGroup.Addon>Kg</InputGroup.Addon>
                                </InputGroup>
                                {errorsAddForm.berat_msc && (
                                    <p style={{ color: 'red' }}>{errorsAddForm.berat_msc}</p>
                                )}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Berat Massa Siap Cetak</Form.ControlLabel>
                                <InputGroup>
                                    <Form.Control
                                        readOnly
                                        value={addTransactionMscForm.percentage_berat_msc || 'masukan nilai massa'}
                                        style={{ background: '#f4f4f4' }}
                                    />
                                    <InputGroup.Addon>%</InputGroup.Addon>
                                </InputGroup>
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Syarat Berat Massa Siap Cetak</Form.ControlLabel>
                                <Form.Control

                                    readOnly
                                    value={
                                        productData?.bobot_min && productData?.bobot_max
                                            ? `${productData.bobot_min} - ${productData.bobot_max} Kg`
                                            : '-'
                                    }
                                    style={{ background: '#f4f4f4' }}
                                />
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Keterangan</Form.ControlLabel>
                                <Form.Control
                                    rows={3}
                                    name="catatan"
                                    componentClass="textarea"
                                    placeholder="catatan"
                                    value={addTransactionMscForm.catatan}
                                    onChange={(value) =>
                                        setAddTransactionMscForm((prev) => ({
                                            ...prev,
                                            catatan: value,
                                        }))
                                    }
                                    readOnly={isViewMode}

                                />
                                {errorsAddForm.catatan && (
                                    <p style={{ color: 'red' }}>{errorsAddForm.catatan}</p>
                                )}
                            </Form.Group>
                            <Form.Group>
                                <Stack justifyContent="end" spacing={10}>
                                    <Button
                                        onClick={() => router.back()}
                                        appearance="subtle"
                                        disabled={loading}
                                    >
                                        {isViewMode ? "Kembali" : "Batal"}
                                    </Button>
                                    {!isViewMode && (
                                        <Button
                                            appearance="primary"
                                            onClick={handleSubmit}
                                            disabled={loading}
                                            loading={loading}
                                        >
                                            {loading ? (isEditMode ? "Memperbarui..." : "Mengirim...") : (isEditMode ? "Update" : "Kirim")}
                                        </Button>
                                    )}
                                </Stack>
                            </Form.Group>
                        </Form>
                    </Panel>

                </div>
            </ContainerLayout>
        </div>
    );
}