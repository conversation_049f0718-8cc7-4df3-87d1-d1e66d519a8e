import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, Notification, SelectPicker, Tooltip, Whisper, Nav, RadioGroup, Radio } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import { useUser } from "@/context/UserContext";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';

//import api
import ApiSiskoTransactionHeader from "@/pages/api/sisko/transaction_h/api_sisko_transaction_h";
import { useRouter } from "next/router";

function SupervisorTransaksiApprovalPage() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState("id_transaksi_header");
    const [sortType, setSortType] = useState("asc");
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const toaster = useToaster();
    const router = useRouter();
    const { user, isAuthenticated, loading: userLoading } = useUser();

    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);

    // Tab state
    const [activeTab, setActiveTab] = useState("transaksi-header");

    // State for Berat MSC Approval Modal
    const [showBeratMscModal, setShowBeratMscModal] = useState(false);
    const [selectedBeratMsc, setSelectedBeratMsc] = useState(null);
    const [approvalStatusBeratMsc, setApprovalStatusBeratMsc] = useState(null);
    const [remarksBeratMsc, setRemarksBeratMsc] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Data states for both tabs
    const [transactionHeadersDataState, setTransactionHeadersDataState] = useState([]);
    const [beratMscDataState, setBeratMscDataState] = useState([]);

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    // Filter data based on active tab
    const getCurrentData = () => {
        return activeTab === "transaksi-header" ? transactionHeadersDataState : beratMscDataState;
    };

    const filteredData = getCurrentData().filter((rowData, i) => {
        const searchFields = activeTab === "transaksi-header"
            ? ["id_transaksi_header", "nama_ppr", "kode_batch", "catatan", "wetmill", "status_transaksi", "dibuat_oleh", "diubah_oleh"]
            : ["id_berat_msc", "kode_batch", "berat_msc", "status_transaksi", "dibuat_oleh", "diubah_oleh"];

        const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : getCurrentData().length;

    const viewHandler = async (idTransHeader) => {
        const url = `${process.env.NEXT_PUBLIC_PIMS_FE}/sisko/approval/transaksi/pdf?idTransHeader=${parseInt(idTransHeader)}`;
        window.open(url, "_blank");
    };

    useEffect(() => {
        if (userLoading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        // Validasi akses menu
        // const validateUserAccess = user?.menu_link_code?.filter((item) => item.includes("sisko/approval"));
        // if (!validateUserAccess || validateUserAccess.length === 0) {
        //   router.push("/dashboard");
        //   return;
        // }
        setModuleName(user.module_name || "");
        setSessionAuth(user);
        HandleGetAllNeedApproveTransactionHeaderApi();
        HandleGetAllBeratMscApi();
    }, [user, userLoading]);

    // Handler for tab change
    const handleTabChange = (eventKey) => {
        setActiveTab(eventKey);
        setSearchKeyword("");
        setPage(1);
    };

    // Render table based on active tab
    const renderTable = () => {
        if (activeTab === "transaksi-header") {
            return renderTransaksiHeaderTable();
        } else {
            return renderBeratMscTable();
        }
    };

    const handleCloseBeratMscModal = () => {
        setShowBeratMscModal(false);
        setSelectedBeratMsc(null);
        setApprovalStatusBeratMsc(null);
        setRemarksBeratMsc("");
    };

    const handleBeratMscApproval = async () => {
        if (!selectedBeratMsc || approvalStatusBeratMsc === null) return;

        setIsSubmitting(true);
        try {
            const payload = {
                id_berat_msc: selectedBeratMsc.id_berat_msc,
                status_transaksi: approvalStatusBeratMsc, // 1 for approve, 0 for reject
                disetujui_oleh: approvalStatusBeratMsc === 1 ? `${sessionAuth.no_karyawan} - ${sessionAuth.username}` : "",
                catatan_persetujuan: approvalStatusBeratMsc === 0 ? remarksBeratMsc : null,
            };

            const res = await ApiSiskoTransactionHeader().editSiskoStatusApprovalTransactionHeaderBeratMsc(payload);

            if (res.status === 200) {
                showNotification("success", "Status persetujuan Berat MSC berhasil diupdate.");
                // Hapus item yang disetujui dari state secara manual, tanpa perlu refetch.
                setBeratMscDataState(prevState =>
                    prevState.filter(item => item.id_berat_msc !== selectedBeratMsc.id_berat_msc)
                );
                handleCloseBeratMscModal();
            } else {
                showNotification("error", res.message || "Gagal mengupdate status.");
            }
        } catch (error) {
            showNotification("error", error.message || "Terjadi kesalahan pada server.");
        } finally {
            setIsSubmitting(false);
        }
    };


    const renderTransaksiHeaderTable = () => {
        return (
            <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={120} align="center" sortable fullText>
                    <HeaderCell>ID Transaksi</HeaderCell>
                    <Cell dataKey="id_transaksi_header" />
                </Column>
                <Column width={200} sortable fullText resizable>
                    <HeaderCell align="center">Nama PPR</HeaderCell>
                    <Cell dataKey="nama_ppr" />
                </Column>
                <Column width={150} sortable fullText>
                    <HeaderCell align="center">Kode Batch</HeaderCell>
                    <Cell dataKey="kode_batch" />
                </Column>
                <Column width={150} sortable fullText>
                    <HeaderCell align="center">Catatan</HeaderCell>
                    <Cell dataKey="catatan" />
                </Column>
                <Column width={150} sortable fullText>
                    <HeaderCell align="center">Wetmill</HeaderCell>
                    <Cell dataKey="wetmill" />
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                    <HeaderCell>Tanggal Dibuat</HeaderCell>
                    <Cell>{(rowData) => rowData.tanggal_dibuat ? new Date(rowData.tanggal_dibuat).toLocaleDateString("en-GB") : ""}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                    <HeaderCell align="center">Dibuat oleh</HeaderCell>
                    <Cell dataKey="dibuat_oleh" />
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                    <HeaderCell>Tanggal Diperbarui</HeaderCell>
                    <Cell>{(rowData) => (rowData.tanggal_diubah ? new Date(rowData.tanggal_diubah).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                    <Cell dataKey="diubah_oleh" />
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                    <HeaderCell>Status</HeaderCell>
                    <Cell>
                        {(rowData) => (
                            <span
                                style={{
                                    color: rowData.status === 1 ? "green" : "red",
                                }}
                            >
                                {rowData.status === 1 ? "Aktif" : "Tidak Aktif"}
                            </span>
                        )}
                    </Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                    <HeaderCell>Status Persetujuan</HeaderCell>
                    <Cell>
                        {(rowData) => {
                            let statusText = "";
                            let statusColor = "";
                            switch (rowData.status_transaksi) {
                                case 0:
                                    statusText = "Menunggu Persetujuan";
                                    statusColor = "orange";
                                    break;
                                case 1:
                                    statusText = "Disetujui";
                                    statusColor = "green";
                                    break;
                                case 2:
                                    statusText = "Ditolak";
                                    statusColor = "red";
                                    break;
                                default:
                                    statusText = "Unknown";
                                    statusColor = "gray";
                            }
                            return (
                                <span style={{ color: statusColor }}>
                                    {statusText}
                                </span>
                            );
                        }}
                    </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                    <HeaderCell>Aksi</HeaderCell>
                    <Cell style={{ padding: "8px" }}>
                        {(rowData) => (
                            <Stack spacing={6}>
                                <Whisper
                                    placement="top"
                                    trigger="hover"
                                    speaker={<Tooltip>Download PDF</Tooltip>}
                                >
                                    <Button
                                        appearance="ghost"
                                        onClick={() => viewHandler(rowData.id_transaksi_header)}
                                    >
                                        <FileDownloadIcon />
                                    </Button>
                                </Whisper>
                                <Whisper
                                    placement="top"
                                    trigger="hover"
                                    speaker={<Tooltip>Konfirmasi</Tooltip>}
                                >
                                    <Button
                                        appearance="ghost"
                                        onClick={() => {
                                            router.push(`/sisko/approval/transaksi/list?id=${rowData.id_transaksi_header}`);
                                        }}
                                    >
                                        <SearchIcon />
                                    </Button>
                                </Whisper>
                            </Stack>
                        )}
                    </Cell>
                </Column>
            </Table>
        );
    };

    const renderBeratMscTable = () => {
        return (
            <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={70} align="center" sortable fullText>
                    <HeaderCell>ID Berat MSC</HeaderCell>
                    <Cell dataKey="id_berat_msc" />
                </Column>
                <Column width={120} align="center" sortable fullText resizable>
                    <HeaderCell>Nama PPR</HeaderCell>
                    <Cell dataKey="nama_ppr" />
                </Column>
                <Column width={150} sortable fullText resizable>
                    <HeaderCell align="center">Kode Batch</HeaderCell>
                    <Cell dataKey="kode_batch" />
                </Column>
                <Column width={150} sortable fullText resizable>
                    <HeaderCell align="center">Berat MSC (kg)</HeaderCell>
                    <Cell dataKey="berat_msc" />
                </Column>
                <Column width={150} sortable fullText resizable>
                    <HeaderCell align="center">Berat Min - Max</HeaderCell>
                    <Cell>
                        {(rowData) => (
                            <span>
                                {rowData.bobot_min} - {rowData.bobot_max}
                            </span>
                        )}
                    </Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                    <HeaderCell>Tanggal Dibuat</HeaderCell>
                    <Cell>{(rowData) => rowData.tanggal_dibuat ? new Date(rowData.tanggal_dibuat).toLocaleDateString("en-GB") : ""}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                    <HeaderCell align="center">Dibuat oleh</HeaderCell>
                    <Cell dataKey="dibuat_oleh" />
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                    <HeaderCell>Tanggal Diperbarui</HeaderCell>
                    <Cell>{(rowData) => (rowData.tanggal_diubah ? new Date(rowData.tanggal_diubah).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                    <Cell dataKey="diubah_oleh" />
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                    <HeaderCell>Status Approval</HeaderCell>
                    <Cell>
                        {(rowData) => {
                            let statusText = "";
                            let statusColor = "";
                            switch (rowData.status_transaksi) {
                                case 0:
                                    statusText = "Ditolak";
                                    statusColor = "red";
                                    break;
                                case 1:
                                    statusText = "Disetujui";
                                    statusColor = "green";
                                    break;
                                case 2:
                                    statusText = "Menunggu Persetujuan";
                                    statusColor = "orange";
                                    break;
                                default:
                                    statusText = "Unknown";
                                    statusColor = "gray";
                            }
                            return (
                                <span style={{ color: statusColor }}>
                                    {statusText}
                                </span>
                            );
                        }}
                    </Cell>
                </Column>
                <Column width={120} fixed="right" align="center">
                    <HeaderCell>Aksi</HeaderCell>
                    <Cell style={{ padding: "8px" }}>
                        {(rowData) => (
                            <Button appearance="primary" size="sm" onClick={() => {
                                setSelectedBeratMsc(rowData);
                                setShowBeratMscModal(true);
                            }}
                                // Tombol hanya aktif jika status "Menunggu Persetujuan" (2)
                                disabled={rowData.status_transaksi !== 2}>
                                Konfirmasi
                            </Button>
                        )}
                    </Cell>
                </Column>
            </Table>
        );
    };

    const HandleGetAllNeedApproveTransactionHeaderApi = async () => {
        try {
            const res = await ApiSiskoTransactionHeader().getAllNeedApproveSiskoTransactionHeader();

            console.log("res", res);
            if (res.status === 200) {
                setTransactionHeadersDataState(res.data);
            } else {
                console.log("error on Get All Api ", res.message);
            }
        } catch (error) {
            console.log("error on catch Get All Api", error);
        }
    };

    const HandleGetAllBeratMscApi = async () => {
        try {
            // Assuming there's an API endpoint for berat MSC approval
            // You may need to create this API endpoint or modify existing one
            const res = await ApiSiskoTransactionHeader().getAllNeedApproveSiskoTransactionHeaderBeratMsc();

            console.log("berat msc res", res);
            if (res.status === 200) {
                setBeratMscDataState(res.data);
            } else {
                console.log("error on GetAllBeratMscApi ", res.message);
            }
        } catch (error) {
            console.log("error on catch GetAllBeratMscApi", error);
        }
    };

    const showNotification = (type, message) => {
        if (type === "success") {
            toaster.push(
                <Notification type="success" header="Success">
                    {message}
                </Notification>,
                { placement: "topEnd" }
            );
        } else if (type === "error") {
            toaster.push(
                <Notification type="error" header="Error">
                    {message}
                </Notification>,
                { placement: "topEnd" }
            );
        }
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Halaman Approval Transaksi Header</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>SISKO</Breadcrumb.Item>
                                    <Breadcrumb.Item>Masterdata</Breadcrumb.Item>
                                    <Breadcrumb.Item>Approval</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Transaksi</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>

                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Halaman Persetujuan</h5>
                            </Stack>
                        }
                    ></Panel>

                    {/* Tab Navigation */}
                    <Panel bordered className="mb-3">
                        <Nav appearance="tabs" activeKey={activeTab} onSelect={handleTabChange}>
                            <Nav.Item eventKey="transaksi-header">
                                Approve Transaksi Header
                            </Nav.Item>
                            <Nav.Item eventKey="berat-msc">
                                Approve Berat MSC
                            </Nav.Item>
                        </Nav>
                    </Panel>

                    <div>
                        <Panel
                            bordered
                            bodyFill
                            header={
                                <Stack justifyContent="space-between">
                                    <h6>
                                        {activeTab === "transaksi-header"
                                            ? "Data Transaksi Header yang Perlu Disetujui"
                                            : "Data Berat MSC yang Perlu Disetujui"
                                        }
                                    </h6>
                                    <InputGroup inside>
                                        <InputGroup.Addon>
                                            <SearchIcon />
                                        </InputGroup.Addon>
                                        <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                                        <InputGroup.Addon
                                            onClick={() => {
                                                setSearchKeyword("");
                                                setPage(1);
                                            }}
                                            style={{
                                                display: searchKeyword ? "block" : "none",
                                                color: "red",
                                                cursor: "pointer",
                                            }}
                                        >
                                            <CloseOutlineIcon />
                                        </InputGroup.Addon>
                                    </InputGroup>
                                </Stack>
                            }
                        >
                            {renderTable()}

                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                                    limitOptions={[10, 30, 50]}
                                    total={totalRowCount}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>
                    </div>
                </div>
            </ContainerLayout >

            {/* Approval Modal for Berat MSC */}
            <Modal
                backdrop="static"
                open={showBeratMscModal}
                onClose={handleCloseBeratMscModal}
                overflow={false}
                size="sm"
            >
                <Modal.Header>
                    <Modal.Title>Konfirmasi Persetujuan Berat MSC</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Stack direction="column" spacing={16} alignItems="stretch">
                        <Panel bordered style={{ padding: "15px", borderRadius: "6px" }}>
                            <Form fluid>
                                <Form.Group>
                                    <Form.ControlLabel>Pilih Tindakan</Form.ControlLabel>
                                    <RadioGroup
                                        inline
                                        name="approvalActionBeratMsc"
                                        value={approvalStatusBeratMsc}
                                        onChange={(value) => {
                                            setApprovalStatusBeratMsc(value);
                                            setRemarksBeratMsc("");
                                        }}
                                    >
                                        <Radio value={1}>
                                            <span style={{ fontWeight: "500", color: "#4CAF50" }}>Setujui</span>
                                        </Radio>
                                        <Radio value={0}>
                                            <span style={{ fontWeight: "500", color: "#F44336" }}>Tolak</span>
                                        </Radio>
                                    </RadioGroup>
                                </Form.Group>

                                {approvalStatusBeratMsc === 0 && (
                                    <Form.Group>
                                        <Form.ControlLabel>Alasan Penolakan</Form.ControlLabel>
                                        <Input
                                            as="textarea"
                                            rows={3}
                                            placeholder="Masukkan alasan penolakan"
                                            value={remarksBeratMsc}
                                            onChange={(value) => setRemarksBeratMsc(value)}
                                            style={{ width: "100%" }}
                                        />
                                    </Form.Group>
                                )}
                            </Form>
                        </Panel>
                    </Stack>
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={handleCloseBeratMscModal} appearance="subtle">
                        Batal
                    </Button>
                    <Button appearance="primary" color={approvalStatusBeratMsc === 1 ? "green" : "red"} disabled={approvalStatusBeratMsc === null || (approvalStatusBeratMsc === 0 && !remarksBeratMsc.trim()) || isSubmitting} loading={isSubmitting} onClick={handleBeratMscApproval}>
                        {approvalStatusBeratMsc === 1 ? "Setujui" : "Tolak"}
                    </Button>
                </Modal.Footer>
            </Modal>
        </div >
    );
}

export default withRoleAccess(SupervisorTransaksiApprovalPage, rolePermissions['sisko/approval/transaksi']);
