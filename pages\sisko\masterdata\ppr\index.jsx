import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, IconButton, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, useToaster, Notification, SelectPicker, Tooltip, Whisper, FlexboxGrid, RadioGroup, Radio } from "rsuite";
import { Trash as TrashIcon, Reload as ReloadIcon } from "@rsuite/icons";
import ContainerLayout from "@/components/layout/ContainerLayout";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import EditIcon from "@rsuite/icons/Edit";
import FileDownloadIcon from "@rsuite/icons/FileDownload";
import { useUser } from "@/context/UserContext";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';

//import api
import ApiMasterdata_ppr from "@/pages/api/sisko/masterdata_ppr/api_masterdata_ppr";
import ApiMasterdataKodeProduk from "@/pages/api/sisko/masterdata_kode_produk/api_masterdata_kode_produk";
import { useRouter } from "next/router";

function MasterDataPprPage() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [sortColumn, setSortColumn] = useState("id_ppr");
  const [sortType, setSortType] = useState("asc");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const toaster = useToaster();
  const router = useRouter();
  const { user, isAuthenticated, loading: userLoading } = useUser();

  const [moduleName, setModuleName] = useState("");
  const [masterPPRDataState, setMasterPPRDataState] = useState([]);
  const [masterPPRActiveDataState, setMasterPPRActiveDataState] = useState([]);
  const [kodeProdukDataState, setKodeProdukDataState] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showActivateModal, setShowActivateModal] = useState(false);



  const emptyMasterPPRForm = {
    nama_ppr: null,
    kode_produk: null,
    id_ppr_copy: 0,
    wetmill: null,
  };

  const emptyEditMasterPPRForm = {
    id_ppr: null,
    nama_ppr: null,
    kode_produk: null,
    wetmill: null,
  };

  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [addLoading, setAddLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(null);

  const [addMasterPPRForm, setAddMasterPPRForm] = useState(emptyMasterPPRForm);
  const [editMasterPPRForm, setEditMasterPPRForm] = useState(emptyMasterPPRForm);
  const [errorsAddForm, setErrorsAddForm] = useState({});
  const [errorsEditForm, setErrorsEditForm] = useState({});
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedPprId, setSelectedPprId] = useState(null);
  const [deleteReason, setDeleteReason] = useState("");
  const [password, setPassword] = useState("");

  const productCodeOptions = kodeProdukDataState.map((produk) => ({
    label: produk.kode_produk,
    value: produk.kode_produk,
    id_produk: produk.id_produk,
  }));


  const showNotification = (type, message, duration = 2000) => {
    toaster.push(
      <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
        <p>{message}</p>
      </Notification>,
      { placement: "topEnd", duration }
    );
  };

  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const handleChangeLimit = (dataKey) => {
    setPage(1);
    setLimit(dataKey);
  };

  const handleSortColumn = (sortColumn, sortType) => {
    setTimeout(() => {
      setSortColumn(sortColumn);
      setSortType(sortType);
    }, 500);
  };

  const filteredData = masterPPRDataState.filter((rowData, i) => {
    const searchFields = ["id_ppr", "nama_ppr", "kode_produk", "approval_oleh", "approval_tanggal", "dibuat_tanggal", "dibuat_oleh", "diubah_tanggal", "diubah_oleh", "dihapus_tanggal", "dihapus_oleh", "status"];

    const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

    return matchesSearch;
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const getFilteredData = () => {
    if (sortColumn && sortType) {
      return filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];
        if (typeof x === "string") {
          x = x.charCodeAt();
        }
        if (typeof y === "string") {
          y = y.charCodeAt();
        }
        if (sortType === "asc") {
          return x - y;
        } else {
          return y - x;
        }
      });
    }
    return filteredData;
  };

  const totalRowCount = searchKeyword ? filteredData.length : masterPPRDataState.length;

  useEffect(() => {
    if (userLoading) return;
    if (!isAuthenticated()) {
      router.push("/login");
      return;
    }
    // if (!user?.menu_link_code?.some((item) => item.includes("pqr/masterdata"))) {
    //   router.push("/dashboard");
    //   return;
    // }
    setModuleName(user.module_name || "");
    HandleGetAllMasterPPRApi();
    HandleGetAllKodeProdukApi();
  }, [userLoading]);

  const handleOpenDeleteModal = (id_ppr) => {
    setSelectedPprId(id_ppr);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedPprId) {
      await handleEditStatusMasterPPRApi(selectedPprId,
        1,
        deleteReason,
        password
      );

      setShowDeleteModal(false);
      setSelectedPprId(null);
      setDeleteReason("");
      setPassword("");
    }
  };

  const handleOpenActivateModal = (id_ppr) => {
    setSelectedPprId(id_ppr);
    setShowActivateModal(true);
    setPassword("");
  };

  const HandleGetAllKodeProdukApi = async () => {
    try {
      const res = await ApiMasterdataKodeProduk().getAllActiveMasterKodeProduk();

      console.log("res", res);
      if (res.status === 200) {
        setKodeProdukDataState(res.data);
      } else {
        console.log("error on GetAllApi ", res.message);
      }
    } catch (error) {
      console.log("error on catch GetAllApi", error);
    }
  };

  const HandleGetAllMasterPPRApi = async () => {
    try {
      const res = await ApiMasterdata_ppr().getAllMasterPPR();

      console.log("res", res);
      if (res.status === 200) {
        setMasterPPRDataState(res.data);
        setMasterPPRActiveDataState(res.data);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };
  // const HandleGetAllActiveMasterPPRApi = async () => {
  //   try {
  //     const res = await ApiMasterdata_ppr().getAllActiveMasterPPR();

  //     console.log("res", res);
  //     if (res.status === 200) {
  //       setMasterPPRActiveDataState(res.data);
  //     } else {
  //       console.log("error on Get All Api ", res.message);
  //     }
  //   } catch (error) {
  //     console.log("error on catch Get All Api", error);
  //   }
  // };

  const HandleAddMasterPPRApi = async () => {
    const errors = {};

    // Check each field and only set the error if it's empty

    if (!addMasterPPRForm.nama_ppr || addMasterPPRForm.nama_ppr.trim() === "") {
      errors.nama_ppr = "Nama PPR Wajib Diisi!";
    }
    if (!addMasterPPRForm.kode_produk) {
      errors.kode_produk = "Kode Produk Wajib Diisi!";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsAddForm(errors);
      return;
    }
    try {
      setAddLoading(true);
      const res = await ApiMasterdata_ppr().createMasterPPR({
        ...addMasterPPRForm,
        dibuat_oleh: user.no_karyawan + " - " + user.username,
      });

      if (res.status === 200) {
        setAddMasterPPRForm(emptyMasterPPRForm);
        setShowAddModal(false);
        await HandleGetAllMasterPPRApi();
        showNotification("success", "PPR baru berhasil Dibuat");
        const newIdPPR = res.id;
        router.push(`/sisko/masterdata/ppr/resep?Id=${newIdPPR}`);
      } else if (res.status === 400) {
        if (res.message === "source PPR does not exist") {
          showNotification("error", "Gagal menambahkan PPR karena sumber tidak ditemukan.");
        } else if (res.message === "no bindings found to copy") {
          showNotification("error", "Gagal menambahkan PPR karena parameter copy kosong.");
        } else {
          showNotification("error", "Gagal menambahkan PPR karena gagal menyalin parameter");
        }
      } else {
        console.log("gagal menambah data", res.message);
        showNotification("error", "gagal menambah data");
      }
    } catch (error) {
      console.log("error gagal menambah data ", error);
      showNotification("error", "gagal menambah data");
    } finally {
      setAddLoading(false);
    }
  };

  const HandleEditMasterPPRApi = async () => {
    const errors = {};

    // Check each field and only set the error if it's empty
    if (!editMasterPPRForm.nama_ppr || editMasterPPRForm.nama_ppr.trim() === "") {
      errors.nama_ppr = "Nama PPR Wajib Diisi!";
    }
    if (!editMasterPPRForm.kode_produk) {
      errors.kode_produk = "Kode Produk Wajib Diisi!";
    }

    // If there are any errors, set them in the state
    if (Object.keys(errors).length > 0) {
      setErrorsEditForm(errors);

      return;
    }
    try {
      setEditLoading(true);
      const res = await ApiMasterdata_ppr().editMasterPPR({
        ...editMasterPPRForm,
        diubah_oleh: user.no_karyawan + " - " + user.username,
      });

      if (res.status === 200) {
        await HandleGetAllMasterPPRApi();
        setShowEditModal(false);
        showNotification("success", "Data Berhasil diedit");
      } else {
        console.log("gagal edit data ", res.message);
        showNotification("error", "gagal mengedit data");
      }
    } catch (error) {
      console.log("gagal edit data ", error);
      toaster.push({ message: "gagal mengedit data", type: "error" });
    } finally {
      setEditLoading(false);
    }
  };

  const handleEditStatusMasterPPRApi = async (id_ppr, status, deleteReason, password) => {
    try {
      setLoading(true);
      const res = await ApiMasterdata_ppr().editStatusMasterPPR({
        id_ppr,
        status,
        catatan_dihapus: deleteReason,
        password: password,
        dihapus_oleh: user.no_karyawan + " - " + user.username,
        employee_id: user.no_karyawan,
      });


      if (res.status === 200) {

        showNotification("success", "Data PPR berhasil dihapus");
        await HandleGetAllMasterPPRApi();
        setDeleteReason("");
        setPassword("");
      } else {
        console.error("Error on update status: ", res.message);
        if (res.message === "wrong username or password") {
          showNotification("error", "Username atau password salah");
          setShowApprovalModal(true);
        } else {
          setDeleteReason("");
          setPassword("");
          console.log("gagal update status ", res.message);
          showNotification("error", `Gagal Update Status`);
        }
      }
    } catch (error) {
      console.log("gagal update status ", error);
      toaster.push({ message: "Gagal Update Status", type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const viewHandler = async (idPPR) => {
    const url = `${process.env.NEXT_PUBLIC_PIMS_FE}/sisko/masterdata/ppr/pdf?idPPR=${parseInt(idPPR)}`;
    window.open(url, "_blank");
  };


  return (
    <div>
      <div>
        <Head>
          <title>Halaman Master PPR</title>
        </Head>
      </div>

      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <div>
                <Breadcrumb>
                  <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                  <Breadcrumb.Item>SISKO</Breadcrumb.Item>
                  <Breadcrumb.Item>PPR</Breadcrumb.Item>
                  <Breadcrumb.Item>Master Data</Breadcrumb.Item>
                  <Breadcrumb.Item active>PPR</Breadcrumb.Item>
                </Breadcrumb>
              </div>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>Halaman Master PPR</h5>
              </Stack>
            }
          ></Panel>
          <div>
            <Panel
              bordered
              bodyFill
              header={
                <Stack justifyContent="space-between">
                  <div className="flex gap-2">
                    <IconButton
                      icon={<PlusRoundIcon />}
                      appearance="primary"
                      onClick={() => {
                        setShowAddModal(true);
                      }}
                    >
                      Tambah
                    </IconButton>
                  </div>

                  <InputGroup inside>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input placeholder="search" value={searchKeyword} onChange={handleSearch} />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                        setPage(1);
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}
                    >
                      <CloseOutlineIcon />
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack>
              }
            >
              <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                <Column width={70} align='center' fixed>
                  <HeaderCell>No</HeaderCell>
                  <Cell>
                    {(rowData, index) => {
                      return index + 1 + limit * (page - 1);
                    }}
                  </Cell>
                </Column>
                <Column width={200} sortable fullText resizable>
                  <HeaderCell align="center">Nama PPR</HeaderCell>
                  <Cell dataKey="nama_ppr" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Kode Produk</HeaderCell>
                  <Cell dataKey="kode_produk" />
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Pembuatan</HeaderCell>
                  <Cell>{(rowData) => new Date(rowData.tanggal_dibuat).toLocaleDateString("en-GB")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dibuat oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.dibuat_oleh}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Diperbarui</HeaderCell>
                  <Cell>{(rowData) => (rowData.tanggal_diubah ? new Date(rowData.tanggal_diubah).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.diubah_oleh}</>}</Cell>
                </Column>
                <Column width={175} sortable resizable align="center" fullText>
                  <HeaderCell>Tanggal Dihapus</HeaderCell>
                  <Cell>{(rowData) => (rowData.tanggal_dihapus ? new Date(rowData.tanggal_dihapus).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={250} sortable resizable fullText>
                  <HeaderCell align="center">Dihapus Oleh</HeaderCell>
                  <Cell>{(rowData) => <>{rowData.dihapus_oleh}</>}</Cell>
                </Column>
                <Column width={120} sortable resizable align="center" fullText>
                  <HeaderCell>Status</HeaderCell>
                  <Cell>
                    {(rowData) => (
                      <span
                        style={{
                          color: rowData.status === 1 ? "green" : "red",
                        }}
                      >
                        {rowData.status === 1 ? "Aktif" : "Tidak Aktif"}
                      </span>
                    )}
                  </Cell>
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Disetujui Oleh</HeaderCell>
                  <Cell dataKey="approval_oleh" />
                </Column>
                <Column width={150} sortable fullText resizable>
                  <HeaderCell align="center">Disetujui Tanggal</HeaderCell>
                  <Cell>{(rowData) => (rowData.approval_tanggal ? new Date(rowData.approval_tanggal).toLocaleDateString("en-GB") : "")}</Cell>
                </Column>
                <Column width={120} fixed="right" align="center" >
                  <HeaderCell>Status Persetujuan</HeaderCell>
                  <Cell>
                    {rowData => {
                      const statusMap = {
                        3: { label: "Draft", color: "blue" },
                        2: { label: "Daftar Tunggu", color: "violet" },
                        1: { label: "Disetujui", color: "green" },
                        0: { label: "Revisi", color: "yellow" }
                      };

                      const isDeleted = rowData?.status === 0;
                      const status = isDeleted ? { label: "Dihapus", color: "red" } : statusMap[rowData?.status_approval] || { label: "Unknown", color: "black" };

                      let tooltipContent = null;
                      if (isDeleted) {
                        tooltipContent = rowData?.catatan_dihapus;
                      } else if (rowData?.status_approval === 0) {
                        tooltipContent = rowData?.catatan_revisi;
                      }

                      return tooltipContent ? (
                        <Whisper placement="top" trigger="hover" speaker={<Tooltip>{tooltipContent}</Tooltip>}>
                          <Tag color={status.color}>{status.label}</Tag>
                        </Whisper>
                      ) : (
                        <Tag color={status.color}>{status.label}</Tag>
                      );
                    }}
                  </Cell>
                </Column>

                <Column width={150} fixed="right" align="center">
                  <HeaderCell>Aksi</HeaderCell>
                  <Cell style={{ padding: "8px" }}>
                    {(rowData) => {

                      return (
                        <div>
                          <Button
                            appearance="subtle"
                            disabled={rowData.status_approval === 1 || rowData.status === 0}
                            onClick={() => {
                              setShowEditModal(true);
                              setEditMasterPPRForm({
                                ...editMasterPPRForm,
                                id_ppr: rowData.id_ppr,
                                nama_ppr: rowData.nama_ppr,
                                kode_produk: rowData.kode_produk,
                                diubah_oleh: user.no_karyawan + " - " + user.username,
                                wetmill: rowData.wetmill,
                              });
                            }}
                          >
                            <EditIcon />
                          </Button>

                          <Button
                            appearance="subtle"
                            onClick={() => {
                              const id_ppr = rowData.id_ppr;

                              if (rowData.status_approval === 1 || rowData.status === 0) {
                                router.push(`/sisko/masterdata/ppr/detailResep?Id=${id_ppr}`);
                              } else {
                                router.push(`/sisko/masterdata/ppr/resep?Id=${id_ppr}`);
                              }
                            }}
                          >
                            <SearchIcon style={{ fontSize: "16px" }} />
                          </Button>

                          <Button
                            appearance="subtle"
                            onClick={() => viewHandler(rowData.id_ppr)}
                          >
                            <FileDownloadIcon />
                          </Button>


                          <Button
                            appearance="subtle"
                            disabled={!(rowData.status_approval === 3 || rowData.status_approval === 1 || rowData.status_approval === 0) || rowData.status === 0}
                            onClick={() => handleOpenDeleteModal(rowData.id_ppr)}
                          >
                            <TrashIcon style={{ fontSize: "16px" }} />
                          </Button>

                        </div>
                      );
                    }}
                  </Cell>
                </Column>


              </Table>

              <div style={{ padding: 20 }}>
                <Pagination
                  prev
                  next
                  first
                  last
                  ellipsis
                  boundaryLinks
                  maxButtons={5}
                  size="xs"
                  layout={["total", "-", "limit", "|", "pager", "skip"]}
                  limitOptions={[10, 30, 50]}
                  total={totalRowCount}
                  limit={limit}
                  activePage={page}
                  onChangePage={setPage}
                  onChangeLimit={handleChangeLimit}
                />
              </div>
            </Panel>
            {/* modal pop up untuk add ppr */}
            <Modal
              backdrop="static"
              open={showAddModal}
              onClose={() => {
                if (!addLoading) {
                  setShowAddModal(false);
                  setAddMasterPPRForm(emptyMasterPPRForm);
                  setErrorsAddForm({});
                }
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Tambah Master PPR</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama PPR</Form.ControlLabel>
                    <Form.Control
                      name="nama_ppr"
                      value={addMasterPPRForm.nama_ppr}
                      onChange={(value) => {
                        setAddMasterPPRForm((prevFormValue) => ({
                          ...prevFormValue,
                          nama_ppr: value,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          nama_ppr: undefined,
                        }));
                      }}
                      disabled={addLoading}
                    />
                    {errorsAddForm.nama_ppr && <p style={{ color: "red" }}>{errorsAddForm.nama_ppr}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                    <SelectPicker
                      data={productCodeOptions}
                      value={addMasterPPRForm.kode_produk}
                      onChange={(value) => {
                        const selectedProduct = productCodeOptions.find((product) => product.value === value);

                        setAddMasterPPRForm((prevFormValue) => ({
                          ...prevFormValue,
                          kode_produk: value,
                          id_produk: selectedProduct ? selectedProduct.id_produk : null,
                        }));
                        setErrorsAddForm((prevErrors) => ({
                          ...prevErrors,
                          kode_produk: undefined,
                        }));
                      }}
                      style={{ width: "100%" }}
                      placeholder="Pilih"
                      disabled={addLoading}
                    />
                    {errorsAddForm.kode_produk && <p style={{ color: "red" }}>{errorsAddForm.kode_produk}</p>}
                  </Form.Group>
                  {/* <FlexboxGrid>
                    <FlexboxGrid.Item colspan={8}>
                      <Form.Group>
                        <Form.ControlLabel>Wetmill</Form.ControlLabel>
                        <RadioGroup
                          name="wetmill"
                          inline
                          value={addMasterPPRForm.wetmill}
                          onChange={(value) => {
                            setAddMasterPPRForm((prevFormValue) => ({
                              ...prevFormValue,
                              wetmill: value,
                            }));
                            setErrorsAddForm((prevErrors) => ({
                              ...prevErrors,
                              wetmill: undefined,
                            }));
                          }}
                        >
                          <Radio value="Y">Yes</Radio>
                          <Radio value="N">No</Radio>
                        </RadioGroup>
                        {errorsAddForm.wetmill && <p style={{ color: "red" }}>{errorsAddForm.wetmill}</p>}
                      </Form.Group>
                    </FlexboxGrid.Item>
                  </FlexboxGrid> */}
                  {/* <Form.Group>
                    <Form.ControlLabel>Menyalin Dari PPR yang Ada (Optional)</Form.ControlLabel>
                    <SelectPicker
                      data={masterPPRActiveDataState.map((ppr) => ({
                        label: `${ppr.nama_ppr} - ${ppr.status === 0 ? "Inactive" :
                          ppr.status_approval === 3 ? "Draft" :
                            ppr.status_approval === 2 ? "Waiting Approval" :
                              ppr.status_approval === 1 ? "Approved" :
                                "Revised"
                          }`,
                        value: ppr.id_ppr
                      }))}
                      style={{ width: "100%" }}
                      onChange={(value) => {
                        setAddMasterPPRForm((prevFormValue) => ({
                          ...prevFormValue,
                          id_ppr_copy: value,
                        }));
                      }}
                      placeholder="Pilih PPR yang Ada"
                    />
                  </Form.Group> */}
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowAddModal(false);
                    setAddMasterPPRForm(emptyMasterPPRForm);
                    setErrorsAddForm({});
                  }}
                  appearance="subtle"
                  disabled={addLoading}
                >
                  Batal
                </Button>
                <Button onClick={HandleAddMasterPPRApi} appearance="primary" loading={addLoading} disabled={addLoading}>
                  Tambah
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal pop up untuk edit ppR */}
            <Modal
              backdrop="static"
              open={showEditModal}
              onClose={() => {
                setShowEditModal(false);
                setEditMasterPPRForm(emptyEditMasterPPRForm);
                setErrorsEditForm({});
              }}
              overflow={false}
            >
              <Modal.Header>
                <Modal.Title>Ubah Master PPR</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Nama PPR</Form.ControlLabel>
                    <Form.Control
                      name="nama_ppr"
                      value={editMasterPPRForm.nama_ppr}
                      onChange={(value) => {
                        setEditMasterPPRForm((prevFormValue) => ({
                          ...prevFormValue,
                          nama_ppr: value,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          nama_ppr: undefined,
                        }));
                      }}
                      disabled={editLoading}
                    />
                    {errorsEditForm.nama_ppr && <p style={{ color: "red" }}>{errorsEditForm.nama_ppr}</p>}
                  </Form.Group>
                  <Form.Group>
                    <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                    <SelectPicker
                      data={productCodeOptions}
                      value={editMasterPPRForm.kode_produk}
                      onChange={(value) => {
                        const selectedProduct = productCodeOptions.find((product) => product.value === value);
                        setEditMasterPPRForm((prevFormValue) => ({
                          ...prevFormValue,
                          kode_produk: value,
                          id_produk: selectedProduct ? selectedProduct.id_produk : null,
                        }));
                        setErrorsEditForm((prevErrors) => ({
                          ...prevErrors,
                          kode_produk: undefined,
                        }));
                      }}
                      style={{ width: "100%" }}
                      disabled={editLoading}
                    />
                    {errorsEditForm.kode_produk && <p style={{ color: "red" }}>{errorsEditForm.kode_produk}</p>}
                  </Form.Group>
                  {/* <FlexboxGrid>
                    <FlexboxGrid.Item colspan={8}>
                      <Form.Group>
                        <Form.ControlLabel>Wetmill</Form.ControlLabel>
                        <RadioGroup
                          name="wetmill"
                          inline
                          value={editMasterPPRForm.wetmill}
                          onChange={(value) => {
                            setEditMasterPPRForm((prevFormValue) => ({
                              ...prevFormValue,
                              wetmill: value,
                            }));
                            setErrorsEditForm((prevErrors) => ({
                              ...prevErrors,
                              wetmill: undefined,
                            }));
                          }}
                        >
                          <Radio value="Y">Yes</Radio>
                          <Radio value="N">No</Radio>
                        </RadioGroup>
                        {errorsEditForm.wetmill && <p style={{ color: "red" }}>{errorsEditForm.wetmill}</p>}
                      </Form.Group>
                    </FlexboxGrid.Item>
                  </FlexboxGrid> */}
                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditMasterPPRForm(emptyMasterPPRForm);
                    setErrorsEditForm({});
                  }}
                  appearance="subtle"
                  disabled={editLoading}
                >
                  Batal
                </Button>
                <Button onClick={HandleEditMasterPPRApi} appearance="primary" loading={editLoading} disabled={editLoading}>
                  Simpan
                </Button>
              </Modal.Footer>
            </Modal>
            {/* modal konfirmasi hapus */}
            <Modal
              backdrop="static"
              open={showDeleteModal}
              onClose={() => {
                setShowDeleteModal(false)
                setDeleteReason("")
                setPassword("");
              }}
            >
              <Modal.Header>
                <Modal.Title>Konfirmasi Hapus</Modal.Title>
              </Modal.Header>
              <Modal.Body>
                <div style={{ marginBottom: '20px', color: 'red' }}>
                  <strong>Perhatian:</strong> Resep yang sudah dihapus tidak akan bisa dipakai kembali
                </div>
                <Form fluid>
                  <Form.Group>
                    <Form.ControlLabel>Alasan Penghapusan <span style={{ color: 'red' }}>*</span></Form.ControlLabel>
                    <Form.Control
                      name="catatan_dihapus"
                      value={deleteReason}
                      onChange={(value) => setDeleteReason(value)}
                      disabled={editLoading}
                    />
                    {!deleteReason && <p style={{ color: "red" }}>Alasan penghapusan harus diisi</p>}
                  </Form.Group>

                </Form>
              </Modal.Body>
              <Modal.Footer>
                <Button onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedPprId(null)
                  setDeleteReason("")
                  setPassword("");
                }} appearance="subtle">
                  Batal
                </Button>
                <Button onClick={handleConfirmDelete} appearance="primary" color="red" disabled={!deleteReason} loading={loading}>
                  Hapus
                </Button>
              </Modal.Footer>
            </Modal>



          </div>
        </div>
      </ContainerLayout>
    </div>
  );
}

export default withRoleAccess(MasterDataPprPage, rolePermissions['sisko/masterdata/ppr']);
