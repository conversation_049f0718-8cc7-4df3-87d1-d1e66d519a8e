// lib/apiClient.js
import axios from "axios";

/**
 * Creates a reusable API function with centralized configuration.
 * @param {string} method - The HTTP method (e.g., 'get', 'post').
 * @param {string} url - The specific API endpoint path (without the base URL).
 * @returns {function} An async function that takes data (payload or params) and optional call-specific Axios config.
 */
const createApiFunction = (method, url) => {
  const lowerCaseMethod = method.toLowerCase();

  return async (data, callSpecificOptions = {}) => {
    const baseURL = process.env.NEXT_PUBLIC_SERVICE;
    if (!baseURL) {
      console.error("NEXT_PUBLIC_SERVICE environment variable is not set.");
      return Promise.reject(new Error("API base URL is not configured."));
    }
    const fullURL = `${baseURL}/${url}`;

    // Base Axios configuration for all requests
    const baseConfig = {
      headers: {
        'ngrok-skip-browser-warning': 'true', // Automatically skip ngrok warning page
        // You can add other common headers here if needed
      },
      // Important if your backend uses cookies/sessions or requires credentials for CORS
      // Ensure your backend CORS configuration allows this!
      withCredentials: true,
      // Allows overriding or adding more Axios config per call if needed
      ...(callSpecificOptions.axiosConfig || {}), // Ensure axiosConfig exists
    };

    // Dynamically add Authorization header if a token exists (example)
    // You'll need a way to get the token, e.g., from localStorage or state management
    const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
    if (token) {
      baseConfig.headers['Authorization'] = `Bearer ${token}`;
    }

    let responsePromise;

    try {
      // Adjust how data and config are passed to Axios based on HTTP method
      if (lowerCaseMethod === 'get' || lowerCaseMethod === 'delete') {
        // For GET/DELETE, 'data' is typically query parameters
        // Axios expects query params in a 'params' object within the config
        responsePromise = axios[lowerCaseMethod](fullURL, {
          ...baseConfig,
          params: data, // 'data' will become the query string, e.g., { id: 1, sort: 'asc' }
        });
      } else if (lowerCaseMethod === 'post' || lowerCaseMethod === 'put' || lowerCaseMethod === 'patch') {
        // For POST/PUT/PATCH, 'data' is the request body (payload)
        // Automatically set Content-Type to application/json if data is an object and not FormData
        if (!(baseConfig.headers && baseConfig.headers['Content-Type']) && typeof data === 'object' && data !== null && !(data instanceof FormData)) {
          baseConfig.headers = { ...baseConfig.headers, 'Content-Type': 'application/json' };
        }
        responsePromise = axios[lowerCaseMethod](fullURL, data, baseConfig);
      } else {
        console.error(`Unsupported HTTP method: ${lowerCaseMethod}`);
        return Promise.reject(new Error(`Unsupported HTTP method: ${lowerCaseMethod}`));
      }

      const res = await responsePromise;
      return res.data; // Return only the data from the response

    } catch (err) {
      console.error(`API call failed: ${lowerCaseMethod.toUpperCase()} ${fullURL}`, err);
      if (err.response) {
        console.error("Error Response Data:", err.response.data);
        console.error("Error Response Status:", err.response.status);
        // Propagate a more informative error object
        const errorToReject = new Error(err.response.data?.message || `API Error: ${err.response.statusText || 'Unknown error'}`);
        // @ts-ignore
        errorToReject.response = err.response; // Attach the full response for further inspection if needed
        return Promise.reject(errorToReject);

      } else if (err.request) {
        console.error("Error Request (no response):", err.request);
        return Promise.reject(new Error("No response from server. Check network connection or server status."));
      } else {
        console.error("Error Message (request setup):", err.message);
        return Promise.reject(new Error(`Error setting up request: ${err.message}`));
      }
    }
  };
};

export default createApiFunction; // Export the function
