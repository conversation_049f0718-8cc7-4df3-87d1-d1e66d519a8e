
import React, { useState } from 'react';
import { <PERSON><PERSON>, Loader } from 'rsuite';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import autoTable from 'jspdf-autotable';

const CpCpkPDFGenerator = ({ namaPPR, selectedBatches, calculationResult, chartRef, resultsRef,username, noKaryawan }) => {
    const [isGenerating, setIsGenerating] = useState(false);

    const handleGeneratePdf = async () => {
        if (!calculationResult || !chartRef.current || !resultsRef.current) {
            console.error("Data atau elemen referensi belum siap.");
            return;
        }
        setIsGenerating(true);

        const doc = new jsPDF('p', 'mm', 'a4');
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const margin = 15;
        let currentY = 20;

        
        doc.setFontSize(18);
        doc.setFont('helvetica', 'bold');
        doc.text('<PERSON><PERSON><PERSON> (Cp & Cpk)', pageWidth / 2, currentY, { align: 'center' });
        currentY += 8;
        doc.setFontSize(14);
        doc.setFont('helvetica', 'normal');
        doc.text(`Produk: ${namaPPR}`, pageWidth / 2, currentY, { align: 'center' });
        currentY += 10;
        doc.setLineWidth(0.5);
        doc.line(margin, currentY, pageWidth - margin, currentY);
        currentY += 10;

        
        try {
            const resultsCanvas = await html2canvas(resultsRef.current, { scale: 2 });
            const resultsImgData = resultsCanvas.toDataURL('image/png');
            const resultsImgProps = doc.getImageProperties(resultsImgData);
            const resultsImgHeight = (resultsImgProps.height * (pageWidth - margin * 2)) / resultsImgProps.width;
            doc.addImage(resultsImgData, 'PNG', margin, currentY, pageWidth - margin * 2, resultsImgHeight);
            currentY += resultsImgHeight + 5;

            doc.setFontSize(11);
            doc.setFont('helvetica', 'bold');
            doc.text('Keterangan Istilah', margin, currentY);
            currentY += 6;

            const definitions = [
                { term: 'USL', definition: 'Batas Spesifikasi Atas (Upper Specification Limit), batas maksimal yang diizinkan.' },
                { term: 'LSL', definition: 'Batas Spesifikasi Bawah (Lower Specification Limit), batas minimal yang diizinkan.' },
                { term: 'Rata-rata', definition: 'Nilai tendensi sentral atau rata-rata dari semua data pengukuran.' },
                { term: 'Std Dev', definition: 'Standar Deviasi, ukuran yang menunjukkan seberapa tersebar data dari nilai rata-ratanya.' },
            ];

            doc.setFontSize(9);
            definitions.forEach(def => {
                doc.setFont('helvetica', 'bold');
                doc.text(`${def.term}:`, margin, currentY, { maxWidth: pageWidth - margin * 2 });
                doc.setFont('helvetica', 'normal');
                doc.text(def.definition, margin + 25, currentY, { maxWidth: pageWidth - margin * 2 - 25 });
                currentY += 10; 
            });
            currentY += 5;

            
            const chartCanvas = await html2canvas(chartRef.current, { scale: 2 });
            const chartImgData = chartCanvas.toDataURL('image/png');
            const chartImgProps = doc.getImageProperties(chartImgData);
            const chartImgHeight = (chartImgProps.height * (pageWidth - margin * 2)) / chartImgProps.width;
            
            
            if (currentY + chartImgHeight > doc.internal.pageSize.getHeight() - margin) {
                doc.addPage();
                currentY = margin;
            }
            
            doc.addImage(chartImgData, 'PNG', margin, currentY, pageWidth - margin * 2, chartImgHeight);
            currentY += chartImgHeight + 10;

            
            doc.addPage();
            autoTable(doc, {
                head: [["No", "Kode Batch", "Berat MSC"]],
                body: selectedBatches.map((batch, index) => [index + 1, batch.kode_batch, batch.berat_msc]),
                startY: margin,
                theme: 'grid',
                headStyles: { fillColor: [22, 160, 133] }
            });

            const totalPages = doc.internal.getNumberOfPages();
            const now = new Date();
            const formattedDate = `${now.getDate().toString().padStart(2, '0')}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getFullYear()}`;
            const formattedTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
            const footerLeftText = `Dokumen ini disediakan oleh : ${noKaryawan} - ${username} pada ${formattedDate} ${formattedTime}`;

            for (let i = 1; i <= totalPages; i++) {
                doc.setPage(i);
                doc.setFontSize(8);
                doc.setFont('helvetica', 'italic');
                doc.setTextColor(128); 

                
                doc.text(footerLeftText, margin, pageHeight - 10);
                
                
                doc.text(`Page ${i} of ${totalPages}`, pageWidth - margin, pageHeight - 10, { align: 'right' });
            }

        } catch (error) {
            console.error("Gagal membuat PDF:", error);
        } finally {
            setIsGenerating(false);
            doc.save(`Laporan_Cp_Cpk_${namaPPR.replace(/\s+/g, '_')}.pdf`);
        }
    };

    return (
        <Button 
            appearance="primary" 
            color="green" 
            onClick={handleGeneratePdf} 
            disabled={isGenerating || !calculationResult}
            className="mt-3"
        >
            {isGenerating ? <Loader content="Membuat PDF..." /> : "📄 Unduh Laporan PDF"}
        </Button>
    );
};

export default CpCpkPDFGenerator;