import { useEffect, useState } from "react";
import Head from "next/head";
import { <PERSON><PERSON>, But<PERSON> } from "rsuite";
import { faFileDownload } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useUser } from "@/context/UserContext";
import ApiResep from "@/pages/api/sisko/resep/api_resep";
import ApiMasterdata_ppr from "@/pages/api/sisko/masterdata_ppr/api_masterdata_ppr";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";

pdfMake.vfs = pdfFonts.vfs;

export default function PdfRecipeComponent({ idPPR, sessionAuth }) {
  const { user } = useUser();
  const [bindingParamPpiDataState, setBindingParamPpiDataState] = useState([]);
  const [formData, setFormData] = useState({
    id_ppr: null,
    nama_ppr: null,
    kode_produk: null,
    wetmill: null,
    tanggal_dibuat: "",
    dibuat_oleh: "",
    diubah_tanggal: "",
    diubah_oleh: "",
    dihapus_tanggal: "",
    dihapus_oleh: "",
  });

  const HandleGetAllBindingParamPpiApi = async (id_ppr) => {
    try {
      const res = await ApiResep().getAllResepByIdPPR({ id_ppr: parseInt(id_ppr) });
      console.log("res", res);
      if (res.status === 200) {
        setBindingParamPpiDataState([...res.data.tahapan_resep]);
        console.log("Updated Data State:", res.data.tahapan_resep);
      } else {
        console.log("error on Get All Api ", res.message);
      }
    } catch (error) {
      console.log("error on catch Get All Api", error);
    }
  };

  const HandleGetDetailPPI = async (id_ppr) => {
    try {
      const api = ApiMasterdata_ppr();
      const response = await api.GetMasterPPRById({ id_ppr: parseInt(id_ppr) });

      if (response.status === 200) {
        const data = response.data;
        setFormData({
          id_ppr: data.id_ppr,
          nama_ppr: data.nama_ppr,
          kode_produk: data.kode_produk,
          wetmill: data.wetmill,
          tanggal_dibuat: new Date(data.tanggal_dibuat).toLocaleDateString("en-GB"),
          dibuat_oleh: data.dibuat_oleh,
          diubah_tanggal: data.diubah_tanggal ? new Date(data.diubah_tanggal).toLocaleDateString("en-GB") : "-",
          diubah_oleh: data.diubah_oleh || "-",
          dihapus_tanggal: data.dihapus_tanggal ? new Date(data.dihapus_tanggal).toLocaleDateString("en-GB") : "-",
          dihapus_oleh: data.dihapus_oleh || "-",
          approval_oleh: data.approval_oleh,
          approval_tanggal: data.approval_tanggal ? new Date(data.approval_tanggal).toLocaleDateString("en-GB") : "-",
        });
      } else {
        console.error("Failed to fetch detail data");
      }
    } catch (error) {
      console.error("Error fetching detail data:", error);
    }
  };

  useEffect(() => {
    HandleGetAllBindingParamPpiApi(idPPR);
    HandleGetDetailPPI(idPPR);

  }, [idPPR]);

  const generatePdfNew = (action) => {
    if (!bindingParamPpiDataState || !Array.isArray(bindingParamPpiDataState)) {
      console.error("Data belum tersedia atau salah format:", bindingParamPpiDataState);
      return;
    }

    // Header sesuai contoh PDF
    const getDisplayValue = (param) => {
      if (param.binding_type === 1) {
        return `${param.min_value} - ${param.max_value}`;
      } else if (param.binding_type === 2) {
        return param.absolute_value;
      } else if (param.binding_type === 3) {
        return param.description_value;
      } else {
        return param.set_point_value || "-";
      }
    };
    const formattedDate = (dateStr) => {
      if (!dateStr) return "-";
      const [day, month, year] = dateStr.split("/");
      // Mengambil 2 digit terakhir dari tahun
      const shortYear = year.slice(-2);
      return `${day}-${month}-${shortYear}`;
    };
    const createdBy = `${formData.dibuat_oleh} pada ${formattedDate(formData.tanggal_dibuat)}`;
    const approvedBy = formData.approval_oleh && formData.approval_tanggal
      ? `${formData.approval_oleh} pada ${formattedDate(formData.approval_tanggal)}`
      : "-";
    const headerSection = [
      { text: `Nama PPR         : ${formData.nama_ppr || '-'}`, style: "detail" },
      { text: `Kode Produk      : ${formData.kode_produk || '-'}`, style: "detail" },
      { text: `Dibuat Oleh      : ${createdBy}`, style: "detail" },
      { text: `Disetujui Oleh   : ${approvedBy}`, style: "detail" },
      { text: "\n" }
    ];

    let contentArray = [];
    contentArray.push(...headerSection);

    // Iterasi tiap langkah kerja (step) dan tampilkan tabel parameter untuk setiap step
    bindingParamPpiDataState.forEach((step) => {
      // Header untuk langkah kerja
      contentArray.push({ text: `Tahapan Langkah : ${step.nama_langkah}`, style: "detail", margin: [0, 10, 0, 5] });

      // Gabungkan parameter dari indikator_y dan indikator_n (jika ada)
      let params = [];
      if (step.indikator_y && Array.isArray(step.indikator_y)) {
        params = params.concat(step.indikator_y);
      }
      if (step.indikator_n && Array.isArray(step.indikator_n)) {
        params = params.concat(step.indikator_n);
      }

      // Urutkan berdasarkan urutan
      params.sort((a, b) => a.urutan - b.urutan);

      // Buat body tabel dengan header kolom
      let tableBody = [
        [
          { text: "No", },
          { text: "Nama Indikator", },
          { text: "Wetmill", },
          { text: "Nilai Set Point", },
          { text: "Aktual", },
          { text: "Persyaratan", },
        ]
      ];

      params.forEach((param, index) => {
        tableBody.push([
          index + 1,
          param.nama_indikator + ` (${param?.uom || "-"})` || "-",
          param.wetmill === "Y" ? "YA" : "TIDAK",
          param.set_point_flag === 1 ? "YA" : "TIDAK",
          param.is_actual === 1 ? "YA" : "TIDAK",
          getDisplayValue(param)
        ]);
      });

      // Masukkan tabel untuk step ini ke konten PDF
      contentArray.push({
        style: "tableExample",
        table: {
          widths: ["auto", "*", "auto", "auto", "auto", "auto"],
          body: tableBody,
        },
      });
    });

    // Format tanggal dibuat agar memakai tanda strip (dd-mm-yyyy)
    const now = new Date();
    const formattedCurrentDate = now.toLocaleDateString("en-GB").split("/").join("-");
    const formattedCurrentTime = now.toLocaleTimeString("en-GB");
    const effectiveSessionAuth = sessionAuth || user;
    const footerText = `Dokumen ini disediakan oleh : ${effectiveSessionAuth?.username || '-'} pada ${formattedCurrentDate} ${formattedCurrentTime}`;

    var dd = {
      content: contentArray,
      styles: {
        headerDetail: {
          fontSize: 12,
          bold: true,
          margin: [0, 2, 0, 2]
        },
        stepHeader: {
          fontSize: 12,
          bold: true,
          decoration: "underline"
        },
        tableHeader: {
          bold: true,
          fontSize: 11,
          color: "black"
        },
        tableExample: {
          margin: [0, 5, 0, 15]
        }
      },
      footer: (currentPage, pageCount) => {
        return {
          columns: [
            { text: footerText, alignment: "left", margin: [30, 0, 0, 0] },
            { text: `Page ${currentPage} of ${pageCount}`, alignment: "right", margin: [0, 0, 30, 0] }
          ]
        };
      },
      pageMargins: [40, 60, 40, 60]
    };

    if (action === "Download") {
      pdfMake.createPdf(dd).download(`Print Out PPR - ${formData.nama_ppr || `Transaction ID ${idPPR}`}.pdf`);
    } else {
      pdfMake.createPdf(dd).open();
    }
  };

  return (
    <div
      style={{
        width: "100%",
        padding: "1em",
        backgroundColor: "#2c2c30",
        position: "sticky",
        top: 0,
      }}
    >
      <Head>
        <title>Reporting Approval PPI</title>
      </Head>
      <Stack justifyContent="space-between">
        <p style={{ color: "white", fontSize: "1em" }}>
          Cetak Laporan Approval PPR - {formData.nama_ppr || `PPR ID : ${idPPR}`}
        </p>
        <Stack>
          <Button onClick={() => generatePdfNew("preview")} style={{ marginRight: "5px" }}>
            Preview
          </Button>
          <Button onClick={() => generatePdfNew("Download")}>
            <FontAwesomeIcon icon={faFileDownload} style={{ fontSize: 15 }} /> Download
          </Button>
        </Stack>
      </Stack>
    </div>
  );
}
