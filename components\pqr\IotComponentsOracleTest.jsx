import React, { useState, useEffect } from "react";
import { Panel, Form, Grid, Row, Col, PanelGroup, Loader } from "rsuite";
import ApiBatchRecipe from "@/pages/api/pqr/transaction_h/api_batch_recipe";

export default function IotComponentsOracleTest({ lot_number }) {
  const [recipeData, setRecipeData] = useState(null);
  const [error, setError] = useState(null);

  const HandleGetRecipe = async (batch_code) => {
    try {
      const res = await ApiBatchRecipe().getRecipeByBatchCode({ lot_number: String(batch_code) });
      if (res.status === 200) {
        setRecipeData(res.data);
      } else {
        console.error("Error fetching data:", res.message);
        setError(res.message);
      }
    } catch (error) {
      console.error("Error on catch:", error);
      setError("Error fetching data");
    }
  };

  useEffect(() => {
    console.log("lot_number received:", lot_number);
    if (lot_number) {
      HandleGetRecipe(String(lot_number));
    }
  }, [lot_number]);

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!recipeData) {
    return (
      <div style={{ textAlign: "center", padding: "2rem" }}>
        <Loader size="md" content="Loading..." vertical />
      </div>
    );
  }

  const { proses, kemas } = recipeData;

  const tableData = [
    { jenis: "Komposisi", detail: proses?.komposisi?.detail?.join(", ") || "-" },
    { jenis: "Komposisi Tambahan", detail: proses?.komposisi_tambahan?.detail?.join(", ") || "-" },
    { jenis: "Summary Test2", detail: proses?.summary_test2?.detail?.join(", ") || "-" },
    { jenis: "Packaging", detail: kemas?.packaging?.detail?.join(", ") || "-" },
  ];

  return (
    <>
      <Panel bordered className="mb-3">
        <Form layout="vertical">
          <div className="container-fluid">
            <div className="row">
              {/* Data Proses - Kiri */}
              <div className="col-md-6">
                <h5 className="mb-3">
                  <strong>Data Proses</strong>
                </h5>
                {recipeData.proses ? (
                  <table className="table table-bordered">
                    <tbody>
                      {[
                        { label: "Recipe No", value: recipeData.proses?.recipe_no },
                        { label: "Lot Number", value: recipeData.proses?.lot_number },
                        { label: "Type PPI", value: recipeData.proses?.type_ppi },
                        { label: "Recipe Description", value: recipeData.proses?.recipe_description },
                        { label: "Metode Analisa", value: recipeData.proses?.metode_analisa },
                      ].map((item, index) =>
                        item.value ? (
                          <tr key={index}>
                            <td>
                              <strong>{item.label}</strong>
                            </td>
                            <td>{item.value}</td>
                          </tr>
                        ) : null
                      )}
                    </tbody>
                  </table>
                ) : (
                  <p className="text-muted">Tidak ada data proses</p>
                )}
              </div>

              {/* Data Kemas - Kanan */}
              <div className="col-md-6">
                <h5 className="mb-3">
                  <strong>Data Kemas</strong>
                </h5>
                {recipeData.kemas ? (
                  <table className="table table-bordered">
                    <tbody>
                      {[
                        { label: "Parent Lot Number", value: recipeData.kemas?.parent_lot_number },
                        { label: "Type PPI", value: recipeData.kemas?.type_ppi },
                        { label: "Recipe2", value: recipeData.kemas?.recipe2 },
                        { label: "Recipe Description", value: recipeData.kemas?.recipe_description },
                        { label: "Shelf Life Days", value: recipeData.kemas?.shelf_life_days },
                      ].map((item, index) =>
                        item.value ? (
                          <tr key={index}>
                            <td>
                              <strong>{item.label}</strong>
                            </td>
                            <td>{item.value}</td>
                          </tr>
                        ) : null
                      )}
                    </tbody>
                  </table>
                ) : (
                  <p className="text-muted">Tidak ada data kemas</p>
                )}
              </div>
            </div>
          </div>
        </Form>
      </Panel>

      {/* {recipeData.proses && (
        <Panel header="Data Proses" bordered className="mb-3">
          <Form layout="vertical">
            <Grid fluid>
              <Row style={{ marginBottom: "16px" }}>
                <Col style={{ paddingRight: "16px" }}>
                  <Form.Group>
                    <Form.ControlLabel>Recipe No</Form.ControlLabel>
                    <Form.Control name="recipe_no" readOnly value={recipeData.proses.recipe_no || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
                <Col style={{ paddingRight: "16px" }}>
                  <Form.Group>
                    <Form.ControlLabel>Iot Number</Form.ControlLabel>
                    <Form.Control name="lot_number" readOnly value={recipeData.proses.lot_number || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
                <Col>
                  <Form.Group>
                    <Form.ControlLabel>Type PPI</Form.ControlLabel>
                    <Form.Control name="type_ppi" readOnly value={recipeData.proses.type_ppi || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
              </Row>
              <Row>
                <Col style={{ paddingRight: "16px" }}>
                  <Form.Group>
                    <Form.ControlLabel>Recipe Description</Form.ControlLabel>
                    <Form.Control name="recipe_description" readOnly value={recipeData.proses.recipe_description || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
                <Col>
                  <Form.Group>
                    <Form.ControlLabel>Metode Analisa</Form.ControlLabel>
                    <Form.Control name="metode_analisa" readOnly value={recipeData.proses.metode_analisa || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
              </Row>
            </Grid>
          </Form>
        </Panel>
      )}

      {recipeData.kemas && (
        <Panel header="Data Kemas" bordered className="mb-3">
          <Form layout="vertical">
            <Grid fluid>
              <Row style={{ marginBottom: "16px" }}>
                <Col style={{ paddingRight: "16px" }}>
                  <Form.Group>
                    <Form.ControlLabel>Parent Lot Number</Form.ControlLabel>
                    <Form.Control name="parent_lot_number" readOnly value={recipeData.kemas.parent_lot_number || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
                <Col style={{ paddingRight: "16px" }}>
                  <Form.Group>
                    <Form.ControlLabel>Type PPI</Form.ControlLabel>
                    <Form.Control name="type_ppi" readOnly value={recipeData.kemas.type_ppi || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
                <Col>
                  <Form.Group>
                    <Form.ControlLabel>Recipe2</Form.ControlLabel>
                    <Form.Control name="recipe2" readOnly value={recipeData.kemas.recipe2 || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
              </Row>
              <Row style={{ marginBottom: "16px" }}>
                <Col style={{ paddingRight: "16px" }}>
                  <Form.Group>
                    <Form.ControlLabel>Recipe Description</Form.ControlLabel>
                    <Form.Control name="recipe_description" readOnly value={recipeData.kemas.recipe_description || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
                <Col style={{ paddingRight: "16px" }}>
                  <Form.Group>
                    <Form.ControlLabel>NIE2</Form.ControlLabel>
                    <Form.Control name="nie2" readOnly value={recipeData.kemas.nie2 || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
                <Col>
                  <Form.Group>
                    <Form.ControlLabel>Shelf Life Days</Form.ControlLabel>
                    <Form.Control name="shelf_life_days" readOnly value={recipeData.kemas.shelf_life_days || ""} style={{ width: "100%" }} />
                  </Form.Group>
                </Col>
              </Row>
            </Grid>
          </Form>
        </Panel>
      )} */}

      {(recipeData.proses || recipeData.kemas) && (
        <Panel header="Data Tambahan" bordered className="mb-3">
          <PanelGroup>
            <Panel header="Komposisi" bordered>
              <ul>{recipeData.proses?.komposisi?.detail?.map((item, index) => <li key={index}>{`${index + 1}. ${item}`}</li>) || "-"}</ul>
            </Panel>
            <Panel header="Komposisi Tambahan" bordered>
              <ul>{recipeData.proses?.komposisi_tambahan?.detail?.map((item, index) => <li key={index}>{`${index + 1}. ${item}`}</li>) || "-"}</ul>
            </Panel>
            <Panel header="Summary Test2" bordered>
              <ul>{recipeData.proses?.summary_test2?.detail?.map((item, index) => <li key={index}>{`${index + 1}. ${item}`}</li>) || "-"}</ul>
            </Panel>
            <Panel header="Packaging" bordered>
              <ul>{recipeData.kemas?.packaging?.detail?.map((item, index) => <li key={index}>{`${index + 1}. ${item}`}</li>) || "-"}</ul>
            </Panel>
          </PanelGroup>
        </Panel>
      )}
    </>
  );
}
