import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Panel } from 'rsuite';
import ContainerLayout from '@/components/layout/ContainerLayout';
import { useUser } from '../context/UserContext';
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';
import ApiDashboard from './api/sisko/dashboard/api_dashboard';

function Dashboard() {
    const router = useRouter();
    const { user, loading } = useUser();
    const [currentTime, setCurrentTime] = useState(new Date());
    const [dashboardData, setDashboardData] = useState([]);
    const [isLoadingActivity, setIsLoadingActivity] = useState(false);
    const [activityError, setActivityError] = useState(null);
    const [isUsingSampleData, setIsUsingSampleData] = useState(false);

    const fetchDashboardData = async () => {
        setIsLoadingActivity(true);
        setActivityError(null);
        setIsUsingSampleData(false);

        try {
            const response = await ApiDashboard().getRecentActivity();
            console.log("1. Full API response:", response);

            // Handle different possible response structures
            let activityData = [];
            if (response?.data?.data) {
                activityData = response.data.data;
            } else if (response?.data) {
                activityData = response.data;
            } else if (Array.isArray(response)) {
                activityData = response;
            } else if (response?.status === 200 && response?.data) {
                // Handle case where data is directly in response.data
                activityData = Array.isArray(response.data) ? response.data : [];
            }

            console.log("2. Processed activity data:", activityData);

            // If no data found, create some sample data for testing
            if (!activityData || activityData.length === 0) {
                console.log("No activity data found, using sample data");
                setIsUsingSampleData(true);
                activityData = [
                    {
                        id: 1,
                        activity: `${user?.username || 'User'} logged into the system`,
                        create_at: new Date().toISOString(),
                    },
                    {
                        id: 2,
                        activity: "Dashboard accessed and loaded successfully",
                        create_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
                    },
                    {
                        id: 3,
                        activity: "System health check completed",
                        create_at: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
                    },
                    {
                        id: 4,
                        activity: "Quality control system initialized",
                        create_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
                    }
                ];
            }

            setDashboardData(Array.isArray(activityData) ? activityData : []);
        } catch (error) {
            console.error('Error fetching dashboard data:', error);

            let errorMessage = 'Failed to load recent activity';
            if (error.message?.includes('Network Error') || error.code === 'ECONNREFUSED') {
                errorMessage = 'Backend service is not available. Showing sample data.';
            } else if (error.response?.status === 404) {
                errorMessage = 'Activity endpoint not found. Showing sample data.';
            } else if (error.response?.status === 401) {
                errorMessage = 'Authentication required. Please login again.';
            } else if (error.response?.status >= 500) {
                errorMessage = 'Server error occurred. Showing sample data.';
            }

            setActivityError(errorMessage);
            setIsUsingSampleData(true);

            // Provide fallback data even on error for better UX
            const fallbackData = [
                {
                    id: 'fallback-1',
                    activity: `${user?.username || 'User'} session active`,
                    create_at: new Date().toISOString(),
                },
                {
                    id: 'fallback-2',
                    activity: "Dashboard loaded with sample data",
                    create_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
                }
            ];
            setDashboardData(fallbackData);
        } finally {
            setIsLoadingActivity(false);
        }
    };

    // Update the time every second
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        // Clean up the interval on component unmount
        return () => clearInterval(timer);
    }, []);

    // SESUDAH (Solusi yang Disarankan)
    // <-- Tambahkan 'user' sebagai dependency

    if (loading || !user) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="flex flex-col items-center space-y-4">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
                    <p className="text-gray-600 font-medium">Loading your dashboard...</p>
                </div>
            </div>
        );
    }

    const getCurrentTime = () => {
        const now = new Date();
        const hour = now.getHours();
        if (hour < 12) return "Good Morning";
        if (hour < 17) return "Good Afternoon";
        return "Good Evening";
    };

    // Format the current time
    const formatTime = (date) => {
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    };

    // Format the current date
    const formatDate = (date) => {
        return date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const handleActionClick = (path) => {
        if (path) {
            router.push(path);
        }
    };

    useEffect(() => {
        // Hanya panggil API jika 'user' sudah ada (tidak null/undefined)
        if (user) {
            console.log("User loaded, fetching dashboard data...", user);
            fetchDashboardData();
        }
    }, [user]);


    // Define quick actions based on user role
    const roleSpecificActions = {
        1: [ // Role 1: Admin
            { title: "Manajemen Role", icon: "➕", color: "bg-green-500", path: "/masterdata-roles" },
            { title: "Manajemen Karyawan", icon: "👥", color: "bg-blue-500", path: "masterdata-users" },

        ],
        2: [ // Role 2: Data Entry / Product Manager
            { title: "Manajemen PPR", icon: "📝", color: "bg-purple-500", path: "/sisko/masterdata/ppr" },
            { title: "Tambah Indikator", icon: "➕", color: "bg-orange-500", path: "/sisko/masterdata/indikator" }
        ],
        3: [ // Role 3: Approver / Manager
            { title: "Persetujuan PPR", icon: "✅", color: "bg-teal-500", path: "/sisko/approval/ppr" },
            { title: "Riwayat Persetujuan", icon: "📜", color: "bg-indigo-500", path: "/sisko/approval/reporting" }
        ],
        4: [ // Role 4: Transaction User
            { title: "Buat Transaksi", icon: "➕", color: "bg-red-500", path: "/sisko/operator/creation/addTransactionHeader" },
            { title: "Persetujuan Transaksi", icon: "📋", color: "bg-yellow-500", path: "/sisko/operator/reporting" }
        ],
        // Default actions if role_id is not found or for other roles
        default: [
            { title: "Lihat Dashboard", icon: "📊", color: "bg-blue-500", path: "/dashboard" },
            { title: "Hubungi Bantuan", icon: "📞", color: "bg-gray-500", path: "/support" }
        ]
    };

    const quickActions = roleSpecificActions[user.role_id] || roleSpecificActions.default;


    const machineData = [
        { name: "Machine Drying", icon: "🔥", status: "Disconnected" },
        { name: "Machine Sifting", icon: "🌪️", status: "Disconnected" },
        { name: "Machine Mixing", icon: "🔄", status: "Disconnected" },
        { name: "Machine Granule", icon: "⚗️", status: "Disconnected" },
        { name: "Machine Final Mixing", icon: "🌀", status: "Disconnected" }
    ];

    return (
        <ContainerLayout>
            <div className="space-y-6 mx-4 md:mx-6 lg:mx-8">
                {/* Welcome Section */}
                <div className="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl p-8 text-white shadow-xl">
                    <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
                        <div>
                            <h1 className="text-3xl font-bold mb-2">
                                {getCurrentTime()}, {user.username}! 👋
                            </h1>
                            <p className="text-green-100 text-lg mb-4">
                                Welcome back to your dashboard. Here's what's happening today.
                            </p>
                        </div>

                        <div className="flex flex-row md:flex-col items-center mt-4 md:mt-0">
                            <div className="w-20 h-20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg mb-0 md:mb-3">
                                <span className="text-3xl">🧪</span>
                            </div>
                            <div className="ml-4 md:ml-0 text-center">
                                <p className="text-white text-xl font-semibold">{formatTime(currentTime)}</p>
                                <p className="text-green-100 text-sm">{formatDate(currentTime)}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Data Connectivity Section */}
                <div>
                    <h5 className="text-lg font-semibold text-gray-700 mb-4">Machine Connectivity</h5>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
                        {machineData.map((machine, index) => (
                            <div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100 flex flex-col items-center text-center">
                                <div className="text-4xl mb-3">{machine.icon}</div>
                                <p className="text-base font-semibold text-gray-800 mb-2 h-12 flex items-center justify-center">{machine.name}</p>
                                <div className="flex items-center">
                                    <span className={`h-2.5 w-2.5 rounded-full mr-2 ${machine.status === 'Connected' ? 'bg-green-500' : 'bg-red-500'}`}></span>
                                    <p className={`text-sm font-medium ${machine.status === 'Connected' ? 'text-gray-700' : 'text-red-600'}`}>{machine.status}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* User Information Card */}
                    <div className="lg:col-span-2">
                        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                    <span className="mr-2">👤</span>
                                    User Information
                                </h3>
                            </div>
                            <div className="p-6">
                                <div className="space-y-4">
                                    {[
                                        { label: "Username", value: user.username, icon: "🏷️" },
                                        { label: "Employee ID", value: user.no_karyawan, icon: "🆔" },
                                        { label: "Email", value: user.email, icon: "📧" },
                                        { label: "Role ID", value: user.role_id, icon: "🔑" }
                                    ].map((item, index) => (
                                        <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                            <div className="flex items-center space-x-3">
                                                <span className="text-xl">{item.icon}</span>
                                                <span className="font-medium text-gray-700">{item.label}</span>
                                            </div>
                                            <span className="text-gray-900 font-semibold">{item.value}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Quick Actions Card */}
                    <div className="space-y-6">
                        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                    <span className="mr-2">⚡</span>
                                    Quick Actions
                                </h3>
                            </div>
                            <div className="p-6">
                                <div className="space-y-3">
                                    {quickActions.map((action, index) => (
                                        <button key={index} onClick={() => handleActionClick(action.path)} className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-left cursor-pointer">
                                            <div className={`w-8 h-8 ${action.color} rounded-lg flex items-center justify-center text-white text-sm`}>
                                                {action.icon}
                                            </div>
                                            <span className="font-medium text-gray-700">{action.title}</span>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </div>

                        {/* Activity Feed */}
                        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
                            <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                                            <span className="mr-2">📈</span>
                                            Recent Activity
                                        </h3>
                                        {isUsingSampleData && (
                                            <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                                Sample Data
                                            </span>
                                        )}
                                    </div>
                                    <button
                                        onClick={fetchDashboardData}
                                        disabled={isLoadingActivity}
                                        className="text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                        title="Refresh activity"
                                    >
                                        <span className={`text-sm ${isLoadingActivity ? 'animate-spin' : ''}`}>🔄</span>
                                    </button>
                                </div>
                            </div>
                            <div className="p-6">
                                <div className="space-y-3">
                                    {isLoadingActivity ? (
                                        // Loading state
                                        <div className="flex items-center justify-center py-8">
                                            <div className="animate-spin rounded-full h-8 w-8 border-2 border-green-500 border-t-transparent"></div>
                                            <span className="ml-3 text-gray-600">Loading recent activity...</span>
                                        </div>
                                    ) : activityError ? (
                                        // Error state
                                        <div className="text-center py-8">
                                            <div className="text-red-500 mb-2">⚠️</div>
                                            <p className="text-sm text-red-600 mb-3">{activityError}</p>
                                            <button
                                                onClick={fetchDashboardData}
                                                className="text-xs bg-red-50 hover:bg-red-100 text-red-600 px-3 py-1 rounded-md transition-colors"
                                            >
                                                Try Again
                                            </button>
                                        </div>
                                    ) : dashboardData && dashboardData.length > 0 ? (
                                        // Data state
                                        dashboardData.map((item, index) => {
                                            // Array warna untuk digunakan secara bergiliran
                                            const colorCycle = ['bg-green-500', 'bg-blue-500', 'bg-purple-500', 'bg-orange-500'];
                                            const dynamicColor = colorCycle[index % colorCycle.length];

                                            // Fungsi untuk format waktu
                                            const formatTimestamp = (ts) => {
                                                if (!ts) return 'No timestamp';
                                                try {
                                                    const date = new Date(ts);
                                                    return date.toLocaleString('id-ID', {
                                                        day: 'numeric',
                                                        month: 'long',
                                                        year: 'numeric',
                                                        hour: '2-digit',
                                                        minute: '2-digit'
                                                    });
                                                } catch (error) {
                                                    return 'Invalid date';
                                                }
                                            };

                                            return (
                                                <div key={item.id || index} className="flex items-start space-x-3">
                                                    <div className={`w-2 h-2 ${dynamicColor} rounded-full mt-2 flex-shrink-0`}></div>
                                                    <div className="flex-1 min-w-0">
                                                        <p className="text-sm text-gray-800 font-medium">
                                                            {item.activity || item.description || 'No activity description'}
                                                        </p>
                                                        <p className="text-xs text-gray-500">
                                                            {formatTimestamp(item.create_at || item.created_at || item.timestamp)}
                                                        </p>
                                                    </div>
                                                </div>
                                            );
                                        })
                                    ) : (
                                        // Empty state
                                        <div className="text-center py-8">
                                            <div className="text-gray-400 mb-2">📊</div>
                                            <p className="text-sm text-gray-500">Tidak ada aktivitas terbaru.</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </ContainerLayout>
    );
}

// Export with role access control - Dashboard is accessible by all roles
export default withRoleAccess(Dashboard, rolePermissions['dashboard']);
