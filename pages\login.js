import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useUser } from '../context/UserContext';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import styles from './Login.module.css';
import { useToaster, Notification } from 'rsuite';

export default function Login() {
  const router = useRouter();
  const toaster = useToaster();
  const { login, loading, isAuthenticated, user } = useUser();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (loading) {
      return;
    }
    if (isAuthenticated()) {
      router.push('/dashboard');
    }
  }, [user, loading, router, isAuthenticated]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const showNotification = (type, message, duration = 4000) => {
    toaster.push(
      <Notification type={type} header={type === 'success' ? 'Sukses' : 'Error'} closable>
        <p>{message}</p>
      </Notification>,
      { placement: 'topEnd', duration }
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const user = await login(formData);
      if (user) {
        showNotification('success', 'Login berhasil! Mengarahkan ke dashboard...');
        router.push('/dashboard');
      } else {
        showNotification('error', 'Username atau password salah.');
      }
    } catch (err) {
      console.error("Login error:", err.message);
      if (err.message === 'user account is inactive') {
        showNotification('error', 'Akun Anda telah dinonaktifkan. Silakan hubungi administrator.');
      } else if (err.message === 'Invalid credentials') {
        showNotification('error', 'Username atau password salah.');
      } else {
        showNotification('error', err.message || 'Terjadi kesalahan saat login.');
      }

    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.formOverlay}>
        <div className={styles.formBox}>
          <h1 className={styles.title}>
            Welcome Back
          </h1>

          {/* {error && (
            <div className={styles.error}>
              {error}
            </div>
          )} */}

          <form onSubmit={handleSubmit}>
            <div className={styles.inputGroup}>
              <label htmlFor="username" className={styles.label}>
                Username
              </label>
              <input
                type="text"
                id="username"
                name="username"
                value={formData.username}
                onChange={handleChange}
                required
                className={styles.input}
              />
            </div>

            <div className={styles.inputGroup}>
              <label htmlFor="password" className={styles.label}>
                Password
              </label>
              <div className={styles.passwordWrapper}>
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className={styles.passwordInput}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={styles.eyeButton}
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className={styles.submitButton}
            >
              {loading ? 'Sedang Login...' : 'Login'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}