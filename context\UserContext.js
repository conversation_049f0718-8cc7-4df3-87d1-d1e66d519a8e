import { createContext, useContext, useState, useEffect } from "react";
import { useRouter } from "next/router";
import ApiAuth from "@/pages/api/auth/auth";

const UserContext = createContext();

export function UserProvider({ children }) {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check if user is already logged in on initial load
  useEffect(() => {
    const checkAuth = () => {
      const token = localStorage.getItem('token');
      const userData = localStorage.getItem('user');
      
      console.log("Checking auth on initial load:");
      console.log("Token:", token);
      console.log("User data:", userData);
      
      if (token && userData) {
        try {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          console.log("User set in context:", parsedUser);
        } catch (error) {
          console.error("Error parsing user data:", error);
          localStorage.removeItem('user');
          localStorage.removeItem('token');
        }
      }
      
      setLoading(false);
    };
    
    checkAuth();
  }, []);

  const login = async (userData) => {
    setLoading(true);
    try {
      console.log("Login attempt with:", userData);
      const response = await ApiAuth().Login(userData);
      console.log("Login API response:", response);

      if (response.status === 200) {
        // Store token in localStorage
        localStorage.setItem('token', response.data.token);
        console.log("Token stored in localStorage:", response.data.token);

        // Store user data in localStorage
        localStorage.setItem('user', JSON.stringify(response.data.user));
        console.log("User data stored in localStorage:", response.data.user);

        // Set user in context
        setUser(response.data.user);

        return response.data.user;
      } else {
        // Handle API-level errors that don't throw (e.g., invalid credentials)
        throw new Error(response.message || 'Invalid username or password');
      }
    } catch (error) {
      console.error("Login error in context:", error);
      setUser(null);
      // Clear any potentially stale auth data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // Re-throw the error so the calling component (login page) can handle it
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    console.log("Logging out user");
    // Clear all user data from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('username');
    localStorage.removeItem('userId');
    localStorage.removeItem('roleId');
    
    // Clear user from context
    setUser(null);
    
    // Redirect to login page
    router.push('/login');
  };

  const isAuthenticated = () => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    console.log("isAuthenticated check - token:", token);
    console.log("isAuthenticated check - userData:", userData);
    
    // If we have a token and user data in localStorage, consider the user authenticated
    // even if the user state in context is not yet set (which can happen during initial load)
    if (token && userData) {
      return true;
    }
    
    // Otherwise, check if we have a user in the context
    return !!user;
  };

  return (
    <UserContext.Provider value={{ user, login, logout, isAuthenticated, loading }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  return useContext(UserContext);
} 