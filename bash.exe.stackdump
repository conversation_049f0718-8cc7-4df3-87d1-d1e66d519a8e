Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FEBA
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210285FF9, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E80  0002100690B4 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9160  00021006A49D (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8991E0000 ntdll.dll
7FF876BA0000 aswhook.dll
7FF897A80000 KERNEL32.DLL
7FF896330000 KERNELBASE.dll
7FF898DA0000 USER32.dll
7FF896B70000 win32u.dll
000210040000 msys-2.0.dll
7FF8971A0000 GDI32.dll
7FF896CF0000 gdi32full.dll
7FF896720000 msvcp_win.dll
7FF896E30000 ucrtbase.dll
7FF8970E0000 advapi32.dll
7FF8977D0000 msvcrt.dll
7FF896F90000 sechost.dll
7FF898BE0000 RPCRT4.dll
7FF895930000 CRYPTBASE.DLL
7FF8967D0000 bcryptPrimitives.dll
7FF899150000 IMM32.DLL
