import { useEffect } from "react";
import { useRouter } from "next/router";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUserCircle } from "@fortawesome/free-solid-svg-icons";
import { Dropdown } from "rsuite";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";
import { useUser } from "@/context/UserContext";

export default function Header() {
    const router = useRouter();
    const MySwal = withReactContent(Swal);
    const { user, logout } = useUser();

    // Use context user info
    const name = user?.username || user?.employee_name || "";
    const email = user?.email || "";

    const logoutHandler = async () => {
        MySwal.fire({
            icon: "warning",
            title: "Are you sure you want to sign out?",
            text: "Signing out will end your current session.",
            showCancelButton: true,
            confirmButtonColor: "#D50000",
            confirmButtonText: "Sign Out",
        }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    // Use context logout
                    logout();
                } catch (error) {
                    console.error("Logout failed", error);
                }
            }
        });
    };


    return (
        <div className="flex w-full p-2 shadow-sm justify-end">
            <div className="flex items-center gap-4">
                <Dropdown
                    title={
                        <div className="flex items-center gap-2">
                            <FontAwesomeIcon icon={faUserCircle} style={{ fontSize: 20 }} />
                            <span>{name}</span>
                        </div>
                    }
                    placement="bottomEnd"
                >
                    <Dropdown.Item panel style={{ padding: "10px" }}>
                        <p>Signed in as</p>
                        <p className="font-semibold capitalize">
                            {name}
                        </p>
                        <p className="text-xs lowercase">{email} - {name}</p>
                    </Dropdown.Item>
                    <Dropdown.Separator />
                    <Dropdown.Item onClick={() => router.push("/user_profile/")}>Profile</Dropdown.Item>
                    <Dropdown.Item onClick={logoutHandler} style={{ color: "#D50000" }}>
                        Sign Out
                    </Dropdown.Item>
                </Dropdown>
            </div>
        </div>
    );
}
