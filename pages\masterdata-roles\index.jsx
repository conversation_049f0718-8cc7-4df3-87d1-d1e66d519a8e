import React, { useEffect, useState } from "react";
import {
    Panel,
    Stack,
    IconButton,
    InputGroup,
    Input,
    Table,
    Pagination,
    Modal,
    Form,
    Button,
    useToaster,
} from "rsuite";
import PlusRoundIcon from "@rsuite/icons/PlusRound";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import TrashIcon from "@rsuite/icons/Trash";
import ReloadIcon from "@rsuite/icons/Reload";
import { useRouter } from "next/router";
import ApiMasterdataRoles from "@/pages/api/masterdata_roles/masterdata_roles";
import { useUser } from "@/context/UserContext";
import ContainerLayout from "@/components/layout/ContainerLayout";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';




function MasterdataRoles() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const toaster = useToaster();
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const router = useRouter();
    const { user, isAuthenticated, loading } = useUser();
    const currentPath = router.pathname;



    const [rolesDataState, setRolesDataState] = useState([]);

    const emptyRoleForm = {
        "role_name": null,
        "description": null,
    };

    const [showAddModal, setShowAddModal] = useState(false);
    const [addRoleForm, setAddRoleForm] = useState(emptyRoleForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [showEditModal, setShowEditModal] = useState(false);
    const [editRoleForm, setEditRoleForm] = useState(emptyRoleForm);
    const [errorsEditForm, setErrorsEditForm] = useState({});

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = rolesDataState.filter((rowData, i) => {
        const searchFields = [
            "id",
            "role_name",
            "description",
            "created_at",
            "updated_at",
            "is_active",
        ];

        const matchesSearch = searchFields.some((field) =>
            rowData[field]
                ?.toString()
                .toLowerCase()
                .includes(searchKeyword.toLowerCase())
        );

        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword
        ? filteredData.length
        : rolesDataState.length;

    useEffect(() => {

        if (loading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }

        HandleGetAllRolesApi();
    }, [user, loading]);

    const HandleGetAllRolesApi = async () => {
        try {
            // Debug token before API call
            const token = localStorage.getItem('token');
            console.log("Token before API call HandleGetAllRolesApi :", token);

            const res = await ApiMasterdataRoles().getAllRoles();

            console.log("API response:", res);
            if (res.status === 200) {
                setRolesDataState(res.data);
            } else {
                console.log("error on GetAllApi ", res.message);
                toaster.push({
                    message: "Error fetching roles: " + res.message,
                    type: "error",
                });

                // If unauthorized, redirect to login
                if (res.status === 401) {
                    toaster.push({
                        message: "Session expired. Please log in again.",
                        type: "error",
                    });
                    router.push("/login");
                }
            }
        } catch (error) {
            console.log("error on catch GetAllApi", error);
            toaster.push({
                message: "Error fetching roles: " + error.message,
                type: "error",
            });
        }
    };

    const HandleAddRoleApi = async () => {
        const errors = {};

        // Check each field and only set the error if it's empty
        if (!addRoleForm.role_name) {
            errors.role_name = "Role name is required";
        }

        if (!addRoleForm.description) {
            errors.description = "Description is required";
        }

        // If there are any errors, set them in the state
        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }
        try {
            const res = await ApiMasterdataRoles().createRole(addRoleForm);

            if (res.status === 200) {
                toaster.push({
                    message: "Role added successfully",
                    type: "success",
                });
                setShowAddModal(false);
                setAddRoleForm(emptyRoleForm);
                HandleGetAllRolesApi();
            } else {
                toaster.push({
                    message: "Error adding role: " + res.message,
                    type: "error",
                });
            }
        } catch (error) {
            toaster.push({
                message: "Error adding role: " + error.message,
                type: "error",
            });
        }
    };

    const handleEditStatus = async (id) => {
        try {
            const role = rolesDataState.find(r => r.id === id);
            const newStatus = role.is_active === 1 ? 1 : 0;

            const res = await ApiMasterdataRoles().updateRoleStatus({
                id: id,
                is_active: newStatus
            });

            if (res.status === 200) {
                toaster.push({
                    message: `Role ${newStatus === 1 ? 'activated' : 'deactivated'} successfully`,
                    type: "success",
                });
                HandleGetAllRolesApi();
            } else {
                toaster.push({
                    message: "Error updating role status: " + res.message,
                    type: "error",
                });
            }
        } catch (error) {
            toaster.push({
                message: "Error updating role status: " + error.message,
                type: "error",
            });
        }
    };

    const handleEditClick = (rowData) => {
        setEditRoleForm({
            id: rowData.id,
            role_name: rowData.role_name,
            description: rowData.description
        });
        setShowEditModal(true);
    };

    const HandleUpdateRoleApi = async () => {
        const errors = {};

        // Check each field and only set the error if it's empty
        if (!editRoleForm.role_name) {
            errors.role_name = "Role name is required";
        }

        if (!editRoleForm.description) {
            errors.description = "Description is required";
        }

        // If there are any errors, set them in the state
        if (Object.keys(errors).length > 0) {
            setErrorsEditForm(errors);
            return;
        }
        try {
            const res = await ApiMasterdataRoles().updateRole(editRoleForm);

            if (res.status === 200) {
                toaster.push({
                    message: "Role updated successfully",
                    type: "success",
                });
                setShowEditModal(false);
                setEditRoleForm(emptyRoleForm);
                HandleGetAllRolesApi();
            } else {
                toaster.push({
                    message: "Error updating role: " + res.message,
                    type: "error",
                });
            }
        } catch (error) {
            toaster.push({
                message: "Error updating role: " + error.message,
                type: "error",
            });
        }
    };

    return (
        <ContainerLayout title="Masterdata Roles">
            <div>
                <Panel
                    bordered
                    bodyFill
                    header={
                        <Stack justifyContent="space-between">
                            <div className="flex gap-2">
                                <IconButton
                                    icon={<PlusRoundIcon />}
                                    appearance="primary"
                                    onClick={() => {
                                        setShowAddModal(true);
                                    }}
                                >
                                    Add
                                </IconButton>
                            </div>

                            <InputGroup inside>
                                <InputGroup.Addon>
                                    <SearchIcon />
                                </InputGroup.Addon>
                                <Input
                                    placeholder="search"
                                    value={searchKeyword}
                                    onChange={handleSearch}
                                />
                                <InputGroup.Addon
                                    onClick={() => {
                                        setSearchKeyword("");
                                        setPage(1);
                                    }}
                                    style={{
                                        display: searchKeyword ? "block" : "none",
                                        color: "red",
                                        cursor: "pointer",
                                    }}
                                >
                                    <CloseOutlineIcon />
                                </InputGroup.Addon>
                            </InputGroup>
                        </Stack>
                    }
                >
                    <Table
                        bordered
                        cellBordered
                        height={400}
                        data={getPaginatedData(getFilteredData(), limit, page)}
                        sortColumn={sortColumn}
                        sortType={sortType}
                        onSortColumn={handleSortColumn}
                    >
                        <Column width={180} align="center" sortable fullText>
                            <HeaderCell>ID Role</HeaderCell>
                            <Cell dataKey="id" />
                        </Column>
                        <Column width={250} sortable fullText>
                            <HeaderCell align="center">
                                Role Name
                            </HeaderCell>
                            <Cell dataKey="role_name" />
                        </Column>
                        <Column width={250} sortable fullText>
                            <HeaderCell align="center">
                                Description
                            </HeaderCell>
                            <Cell dataKey="description" />
                        </Column>
                        <Column width={175} sortable resizable align="center" fullText>
                            <HeaderCell>Created Date</HeaderCell>
                            <Cell>
                                {(rowData) =>
                                    new Date(rowData.created_at).toLocaleDateString("en-GB")
                                }
                            </Cell>
                        </Column>
                        <Column width={175} sortable resizable align="center" fullText>
                            <HeaderCell>Updated Date</HeaderCell>
                            <Cell>
                                {(rowData) =>
                                    rowData.updated_at ? new Date(rowData.updated_at).toLocaleDateString("en-GB") : ''
                                }
                            </Cell>
                        </Column>
                        <Column width={120} sortable resizable align="center" fullText>
                            <HeaderCell>Status</HeaderCell>
                            <Cell>
                                {(rowData) => (
                                    <span
                                        style={{
                                            color: rowData.is_active === 1 ? "green" : "red",
                                        }}
                                    >
                                        {rowData.is_active === 1 ? "Active" : "Inactive"}
                                    </span>
                                )}
                            </Cell>
                        </Column>
                        <Column width={120} fixed="right" align="center">
                            <HeaderCell>Action</HeaderCell>
                            <Cell style={{ padding: "8px" }}>
                                {(rowData) => (
                                    <div>
                                        <Button
                                            appearance="link"
                                            disabled={rowData.is_active === 0}
                                            onClick={() => handleEditClick(rowData)}
                                        >
                                            Edit
                                        </Button>
                                        <Button
                                            appearance="subtle"
                                            onClick={() => handleEditStatus(rowData.id)}
                                        >
                                            {rowData.is_active === 1 ? (
                                                <TrashIcon style={{ fontSize: "16px" }} />
                                            ) : (
                                                <ReloadIcon style={{ fontSize: "16px" }} />
                                            )}
                                        </Button>
                                    </div>
                                )}
                            </Cell>
                        </Column>
                    </Table>

                    <div style={{ padding: 20 }}>
                        <Pagination
                            prev
                            next
                            first
                            last
                            ellipsis
                            boundaryLinks
                            maxButtons={5}
                            size="xs"
                            layout={["total", "-", "limit", "|", "pager", "skip"]}
                            limitOptions={[10, 30, 50]}
                            total={totalRowCount}
                            limit={limit}
                            activePage={page}
                            onChangePage={setPage}
                            onChangeLimit={handleChangeLimit}
                        />
                    </div>
                </Panel>

                <Modal
                    backdrop="static"
                    open={showAddModal}
                    onClose={() => {
                        setShowAddModal(false);
                        setAddRoleForm(emptyRoleForm);
                        setErrorsAddForm({});
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Add Role</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>
                                    Role Name
                                </Form.ControlLabel>
                                <Form.Control
                                    name="role_name"
                                    value={addRoleForm.role_name}
                                    onChange={(value) => {
                                        setAddRoleForm((prevFormValue) => ({
                                            ...prevFormValue,
                                            role_name: value,
                                        }));
                                        setErrorsAddForm((prevErrors) => ({
                                            ...prevErrors,
                                            role_name: undefined,
                                        }));
                                    }}
                                />
                                {errorsAddForm.role_name && <p style={{ color: 'red' }}>{errorsAddForm.role_name}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>
                                    Description
                                </Form.ControlLabel>
                                <Form.Control
                                    name="description"
                                    value={addRoleForm.description}
                                    onChange={(value) => {
                                        setAddRoleForm((prevFormValue) => ({
                                            ...prevFormValue,
                                            description: value,
                                        }));
                                        setErrorsAddForm((prevErrors) => ({
                                            ...prevErrors,
                                            description: undefined,
                                        }));
                                    }}
                                />
                                {errorsAddForm.description && <p style={{ color: 'red' }}>{errorsAddForm.description}</p>}
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowAddModal(false);
                                setAddRoleForm(emptyRoleForm);
                                setErrorsAddForm({});
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                HandleAddRoleApi();
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Add
                        </Button>
                    </Modal.Footer>
                </Modal>

                <Modal
                    backdrop="static"
                    open={showEditModal}
                    onClose={() => {
                        setShowEditModal(false);
                        setEditRoleForm(emptyRoleForm);
                        setErrorsEditForm({});
                    }}
                    overflow={false}
                >
                    <Modal.Header>
                        <Modal.Title>Edit Role</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form fluid>
                            <Form.Group>
                                <Form.ControlLabel>
                                    Role Name
                                </Form.ControlLabel>
                                <Form.Control
                                    name="role_name"
                                    value={editRoleForm.role_name}
                                    onChange={(value) => {
                                        setEditRoleForm((prevFormValue) => ({
                                            ...prevFormValue,
                                            role_name: value,
                                        }));
                                        setErrorsEditForm((prevErrors) => ({
                                            ...prevErrors,
                                            role_name: undefined,
                                        }));
                                    }}
                                />
                                {errorsEditForm.role_name && <p style={{ color: 'red' }}>{errorsEditForm.role_name}</p>}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>
                                    Description
                                </Form.ControlLabel>
                                <Form.Control
                                    name="description"
                                    value={editRoleForm.description}
                                    onChange={(value) => {
                                        setEditRoleForm((prevFormValue) => ({
                                            ...prevFormValue,
                                            description: value,
                                        }));
                                        setErrorsEditForm((prevErrors) => ({
                                            ...prevErrors,
                                            description: undefined,
                                        }));
                                    }}
                                />
                                {errorsEditForm.description && <p style={{ color: 'red' }}>{errorsEditForm.description}</p>}
                            </Form.Group>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button
                            onClick={() => {
                                setShowEditModal(false);
                                setEditRoleForm(emptyRoleForm);
                                setErrorsEditForm({});
                            }}
                            appearance="subtle"
                        >
                            Cancel
                        </Button>
                        <Button
                            onClick={() => {
                                HandleUpdateRoleApi();
                            }}
                            appearance="primary"
                            type="submit"
                        >
                            Update
                        </Button>
                    </Modal.Footer>
                </Modal>
            </div>
        </ContainerLayout>
    );
}

export default withRoleAccess(MasterdataRoles, rolePermissions['masterdata-roles']);