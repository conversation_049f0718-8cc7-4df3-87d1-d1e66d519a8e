import createApiFunction from "@/lib/apiClient";

export default function ApiRekonKaplet() {
  return {
    getAllRekonKaplet: createApiFunction("get", "sisko/rekon_kaplet/list"),
    getRekonKapletbyIdTransHeader: createApiFunction("post", "sisko/rekon_kaplet/id-trans-header"),
    createRekonKaplet: createApiFunction("post", "sisko/rekon_kaplet/create"),
    editRekonKaplet: createApiFunction("put", "sisko/rekon_kaplet/edit"),
  };
}
