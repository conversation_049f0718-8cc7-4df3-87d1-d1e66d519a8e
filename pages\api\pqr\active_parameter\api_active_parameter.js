import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_REAGEN}/pqr/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiActiveParameter() {
  return {
    getAllActiveParameter: createApiFunction("get","active_parameter/list"),
  };
}
