import React from 'react';
import { Panel, Nav } from 'rsuite';
import {
    LayoutDashboard,
    User,
    Settings
} from 'lucide-react';

const Sidebar = ({ activeKey, onSelect, collapsed }) => {
    return (
        <Panel
            bordered
            style={{
                height: '100%',
                transition: 'all 0.3s ease',
                width: collapsed ? '60px' : '240px'
            }}
        >
            <Nav
                vertical
                activeKey={activeKey}
                onSelect={onSelect}
                style={{ height: '100%' }}
            >
                <Nav.Item
                    eventKey="dashboard"
                    icon={<LayoutDashboard size={18} />}
                    style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                    }}
                >
                    {!collapsed && 'Dashboard'}
                </Nav.Item>
                <Nav.Item
                    eventKey="profile"
                    icon={<User size={18} />}
                    style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                    }}
                >
                    {!collapsed && 'Profile'}
                </Nav.Item>
                <Nav.Item
                    eventKey="settings"
                    icon={<Settings size={18} />}
                    style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                    }}
                >
                    {!collapsed && 'Settings'}
                </Nav.Item>
            </Nav>
        </Panel>
    );
};

export default Sidebar; 