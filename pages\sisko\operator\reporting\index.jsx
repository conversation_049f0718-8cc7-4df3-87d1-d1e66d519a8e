import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import Head from "next/head";
import ContainerLayout from "@/components/layout/ContainerLayout";
import ApiSiskoTransactionHeader from "@/pages/api/sisko/transaction_h/api_sisko_transaction_h";
import { Panel, Stack, Breadcrumb, Loader, Table, Button, Input, InputGroup, Pagination, useToaster, Notification } from "rsuite";
import { useUser } from "@/context/UserContext";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';
import SearchIcon from '@rsuite/icons/Search';
import FileDownloadIcon from '@rsuite/icons/FileDownload';

function SiskoReporting() {
    const router = useRouter();
    const [loading, setLoading] = useState(true);
    const [transactionHeadersDataState, setTransactionHeadersDataState] = useState([]);
    const { user, isAuthenticated, loading: userLoading } = useUser();
    const [sessionAuth, setSessionAuth] = useState(null);
    const [moduleName, setModuleName] = useState("");
    const toaster = useToaster();

    // Pagination and sorting states
    const [page, setPage] = useState(1);
    const [limit, setLimit] = useState(10);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [searchKeyword, setSearchKeyword] = useState('');

    // Format date to dd/mm/yyyy
    const formatDate = (dateString) => {
        if (!dateString) return "-";
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return "-";

        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    };

    // Handle search
    const handleSearchChange = (value) => {
        setSearchKeyword(value);
        setPage(1); // Reset to first page when searching
    };

    // Filter data based on search keyword
    const filteredData = searchKeyword
        ? transactionHeadersDataState.filter((item) => {
            const keyword = searchKeyword.toLowerCase();
            return (
                (item.nama_ppr && item.nama_ppr.toLowerCase().includes(keyword)) ||
                (item.kode_batch && item.kode_batch.toLowerCase().includes(keyword)) ||
                (item.dibuat_oleh && item.dibuat_oleh.toLowerCase().includes(keyword)) ||
                (item.disetujui_oleh && item.disetujui_oleh.toLowerCase().includes(keyword))
            );
        })
        : transactionHeadersDataState;

    // Sorting function
    const handleSortColumn = (sortColumn, sortType) => {
        setSortColumn(sortColumn);
        setSortType(sortType);
    };

    // Get sorted and filtered data
    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.toLowerCase();
                }
                if (typeof y === "string") {
                    y = y.toLowerCase();
                }
                if (sortType === "asc") {
                    return x > y ? 1 : -1;
                } else {
                    return x < y ? 1 : -1;
                }
            });
        }
        return filteredData;
    };

    // Get paginated data
    const getPaginatedData = (data, limit, page) => {
        const start = (page - 1) * limit;
        const end = start + limit;
        return data.slice(start, end);
    };

    const totalRowCount = searchKeyword ? filteredData.length : transactionHeadersDataState.length;

    // View PDF handler
    const viewHandler = async (idTransHeader) => {
        const url = `${process.env.NEXT_PUBLIC_PIMS_FE}/sisko/operator/reporting/pdf?idTransHeader=${parseInt(idTransHeader)}`;
        window.open(url, "_blank");
    };

    // Show notification
    const showNotification = (type, message) => {
        if (type === "success") {
            toaster.push(
                <Notification type="success" header="Success">
                    {message}
                </Notification>,
                { placement: "topEnd" }
            );
        } else if (type === "error") {
            toaster.push(
                <Notification type="error" header="Error">
                    {message}
                </Notification>,
                { placement: "topEnd" }
            );
        }
    };

    // Fetch data on component mount
    useEffect(() => {
        if (userLoading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        setModuleName(user.module_name || "");
        setSessionAuth(user);
        HandleGetAllFullyApproveTransactionHeaderApi();
    }, [user, userLoading]);

    // API call to get fully approved transactions
    const HandleGetAllFullyApproveTransactionHeaderApi = async () => {
        try {
            setLoading(true);
            const res = await ApiSiskoTransactionHeader().getAllFullyApproveSiskoTransactionHeader();

            console.log("res", res);
            if (res.status === 200) {
                setTransactionHeadersDataState(res.data || []);
            } else {
                console.log("error on Get All Api ", res.message);
                showNotification("error", "Gagal mengambil data transaksi");
            }
        } catch (error) {
            console.log("error on catch Get All Api", error);
            showNotification("error", "Terjadi kesalahan saat mengambil data");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div>
            <Head>
                <title>Laporan Transaksi Disetujui</title>
            </Head>
            <ContainerLayout title="Halaman Transaksi Disetujui">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <Breadcrumb>
                                <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
                                <Breadcrumb.Item href="/sisko/operator">Sisko</Breadcrumb.Item>
                                <Breadcrumb.Item active>Reporting Transaksi Disetujui</Breadcrumb.Item>
                            </Breadcrumb>
                        </Stack.Item>
                    </Stack>

                    <Panel bordered>
                        <Stack spacing={10} justifyContent="space-between" className="mb-3">
                            <h5>Daftar Transaksi Disetujui</h5>
                            <InputGroup inside style={{ width: 300 }}>
                                <Input
                                    placeholder="Cari..."
                                    value={searchKeyword}
                                    onChange={handleSearchChange}
                                />
                                <InputGroup.Addon>
                                    <SearchIcon />
                                </InputGroup.Addon>
                            </InputGroup>
                        </Stack>

                        {loading ? (
                            <Loader center content="Memuat data..." />
                        ) : (
                            <>
                                <Table
                                    data={getPaginatedData(getFilteredData(), limit, page)}
                                    bordered
                                    cellBordered
                                    sortColumn={sortColumn}
                                    height={400}
                                    sortType={sortType}
                                    onSortColumn={handleSortColumn}
                                >
                                    <Table.Column width={70} align="center" fixed>
                                        <Table.HeaderCell>No</Table.HeaderCell>
                                        <Table.Cell>
                                            {(rowData, rowIndex) => {
                                                return (rowIndex + 1) + (page - 1) * limit;
                                            }}
                                        </Table.Cell>
                                    </Table.Column>

                                    <Table.Column width={150} sortable resizable>
                                        <Table.HeaderCell>ID Transaksi</Table.HeaderCell>
                                        <Table.Cell dataKey="id_transaksi_header" />
                                    </Table.Column>

                                    <Table.Column width={200} sortable resizable>
                                        <Table.HeaderCell>Nama PPR</Table.HeaderCell>
                                        <Table.Cell dataKey="nama_ppr" />
                                    </Table.Column>

                                    <Table.Column width={150} sortable resizable>
                                        <Table.HeaderCell>Kode Batch</Table.HeaderCell>
                                        <Table.Cell dataKey="kode_batch" />
                                    </Table.Column>

                                    <Table.Column width={150} sortable resizable>
                                        <Table.HeaderCell>Tanggal Dibuat</Table.HeaderCell>
                                        <Table.Cell>
                                            {(rowData) => formatDate(rowData.tanggal_dibuat)}
                                        </Table.Cell>
                                    </Table.Column>

                                    <Table.Column width={200} sortable resizable>
                                        <Table.HeaderCell>Dibuat Oleh</Table.HeaderCell>
                                        <Table.Cell dataKey="dibuat_oleh" />
                                    </Table.Column>

                                    <Table.Column width={150} sortable resizable>
                                        <Table.HeaderCell>Tanggal Disetujui</Table.HeaderCell>
                                        <Table.Cell>
                                            {(rowData) => formatDate(rowData.disetujui_tanggal)}
                                        </Table.Cell>
                                    </Table.Column>

                                    <Table.Column width={200} sortable resizable>
                                        <Table.HeaderCell>Disetujui Oleh</Table.HeaderCell>
                                        <Table.Cell dataKey="disetujui_oleh" />
                                    </Table.Column>

                                    <Table.Column width={120} fixed="right" align="center">
                                        <Table.HeaderCell>Aksi</Table.HeaderCell>
                                        <Table.Cell style={{ padding: "8px" }}>
                                            {(rowData) => {
                                                return (
                                                    <div style={{ display: "flex", gap: "8px", justifyContent: "center" }}>
                                                        <Button
                                                            appearance="ghost"
                                                            onClick={() => viewHandler(rowData.id_transaksi_header)}
                                                        >
                                                            <FileDownloadIcon />
                                                        </Button>
                                                        <Button
                                                            appearance="ghost"
                                                            onClick={() => {
                                                                router.push(`/sisko/operator/creation/list?id=${rowData.id_transaksi_header}`);
                                                            }}
                                                        >
                                                            <SearchIcon />
                                                        </Button>
                                                    </div>
                                                );
                                            }}
                                        </Table.Cell>
                                    </Table.Column>
                                </Table>

                                <div style={{ padding: 20 }}>
                                    <Pagination
                                        prev
                                        next
                                        first
                                        last
                                        ellipsis
                                        boundaryLinks
                                        maxButtons={5}
                                        size="xs"
                                        layout={["total", "-", "limit", "|", "pager", "skip"]}
                                        limitOptions={[10, 30, 50]}
                                        total={totalRowCount}
                                        limit={limit}
                                        activePage={page}
                                        onChangePage={setPage}
                                        onChangeLimit={(limit) => {
                                            setPage(1);
                                            setLimit(limit);
                                        }}
                                    />
                                </div>
                            </>
                        )}
                    </Panel>
                </div>
            </ContainerLayout>
        </div>
    );
}

export default withRoleAccess(SiskoReporting, rolePermissions['sisko/operator/reporting']);