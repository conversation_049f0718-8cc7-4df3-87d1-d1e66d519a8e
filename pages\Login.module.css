/* Login.module.css */

/* Style untuk Desktop (Layar Besar) */
.container {
  min-height: 100vh;
  position: relative;
  font-family: Arial, sans-serif;
  background-image: url("/setengah.png"); /* Pastikan path gambar benar */
  background-size: cover;
  background-position: center;
}

.formOverlay {
  position: absolute;
  top: 0;
  right: 0;
  width: 650px;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.formBox {
  background-color: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 380px;
}

.title {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.error {
  background-color: #fee2e2;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.inputGroup {
  margin-bottom: 1.5rem;
}

.label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4b5563;
  font-size: 0.875rem;
}

.input {
  width: 100%;
  padding: 0.75rem;
  border-radius: 5px;
  border: 1px solid #d1d5db;
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.2s;
}

.passwordWrapper {
  position: relative;
}

.passwordInput {
  /* sama seperti input biasa tapi dengan padding kanan untuk ikon */
  composes: input;
  padding-right: 2.5rem;
}

.eyeButton {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  cursor: pointer;
  color: #6b7280;
  font-size: 1.1rem;
}

.submitButton {
  width: 100%;
  padding: 0.75rem;
  background-color: #16a34a;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submitButton:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

/* --- INI BAGIAN PENTING UNTUK TAMPILAN HP --- */
/* Media Query untuk layar dengan lebar maksimal 768px */
@media (max-width: 768px) {
  .container {
    background-image: none; /* Hapus background image */
    background-color: #f3f4f6; /* Ganti dengan warna latar belakang */
  }

  .formOverlay {
    position: relative; /* Bukan lagi absolute */
    width: 100%; /* Lebar penuh */
    right: auto; /* Hapus posisi kanan */
    height: auto;
    min-height: 100vh; /* Pastikan tetap setinggi layar */
    background-color: rgba(
      0,
      0,
      0,
      0.3
    ); /* Latar belakang gelap agar form lebih terbaca */
  }

  .formBox {
    max-width: 90%; /* Batasi lebar form di HP */
    margin: 0 auto; /* Form di tengah */
    padding: 2rem;
  }

  .title {
    font-size: 1.6rem;
  }
}
