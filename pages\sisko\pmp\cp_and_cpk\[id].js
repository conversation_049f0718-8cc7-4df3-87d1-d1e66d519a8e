import { useRouter } from "next/router";
import { useEffect, useState, useRef  } from "react";
import Head from "next/head";
import ContainerLayout from "@/components/layout/ContainerLayout";
import ApiBeratMSC from "@/pages/api/sisko/berat_msc/api_berat_msc";
import { Panel, Stack, Breadcrumb, Loader, Table, Button, Checkbox, Notification, useToaster, Col, Row, Placeholder } from "rsuite";
import CapabilityChart from "@/components/CapabilityChart";
import CpCpkPDFGenerator from '@/components/CpCpkPDFGenerator';
import { useUser } from "@/context/UserContext";


const CalculationResultDisplay = ({ result }) => {
    if (!result) return null;

    const { cp, cpk, mean, stdDev, usl, lsl } = result;

    const getCpSummary = (cpValue) => {
        if (cpValue > 1.33) return "Potensi proses sangat baik.";
        if (cpValue >= 1.0) return "Potensi proses cukup.";
        return "Potensi proses perlu peningkatan.";
    };

    const getCpkSummary = (cpkValue) => {
        if (cpkValue >= 1.33) return "Proses sangat mampu (highly capable).";
        if (cpkValue >= 1.0) return "Proses mampu (capable).";
        if (cpkValue > 0) return "Proses tidak mampu (not capable).";
        return "Proses di luar batas spesifikasi.";
    };
    const getInterpretations = (cpValue, cpkValue) => {
        const interpretations = [];
        const tolerance = 0.01;
        if (cpValue > 1.33) {
            interpretations.push("Nilai Cp > 1.33, menandakan potensi kapabilitas proses sangat baik.");
        }
        if (cpValue < 1.0) {
            interpretations.push("Nilai Cp < 1.0, menandakan proses tidak capable untuk memenuhi spesifikasi.");
        }

        
        if (cpkValue < 0) {
            interpretations.push("Nilai Cpk negatif, menandakan rata-rata proses berada di luar batas spesifikasi.");
        } else if (Math.abs(cpkValue) < tolerance) {
            interpretations.push("Nilai Cpk = 0, menandakan rata-rata proses sama dengan salah satu batas spesifikasi (USL/LSL).");
        } else if (cpkValue < 1.0) {
            interpretations.push("Nilai Cpk < 1.0, menandakan proses menghasilkan produk yang tidak sesuai dengan spesifikasi.");
        } else if (Math.abs(cpkValue - 1.0) < tolerance) {
            interpretations.push("Nilai Cpk = 1.0, menandakan satu sisi sebaran proses menyentuh batas spesifikasi.");
        } else if (cpkValue > 1.0 && cpkValue < 1.33) {
            interpretations.push("Nilai Cpk > 1.0, menandakan proses mampu (capable) untuk memenuhi spesifikasi.");
        } else if (cpkValue >= 1.33) {
           
            interpretations.push("Nilai Cpk >= 1.33, menandakan proses sangat mampu (highly capable).");
        }
        
    
        if (Math.abs(cpValue - cpkValue) < tolerance) {
            interpretations.push("Nilai Cp ≈ Cpk, menandakan bahwa proses berjalan tepat di tengah-tengah target spesifikasi.");
        }

        return interpretations;
    };

    const notes = getInterpretations(cp, cpk);

    return (
        <Panel header={<h3>📊 Hasil Analisis Kapabilitas Proses</h3>} bordered className="mt-4">
            <Row gutter={30}>
                <Col md={12}>
                    <Panel bordered className="text-center">
                        <p className="font-bold text-lg">Cp (Process Capability)</p>
                        <p className="text-4xl font-bold text-blue-600">{cp.toFixed(2)}</p>
                        {/* Menggunakan fungsi bantuan baru */}
                        <p className="mt-2">{getCpSummary(cp)}</p>
                    </Panel>
                </Col>
                <Col md={12}>
                    <Panel bordered className="text-center">
                        <p className="font-bold text-lg">Cpk (Process Capability Index)</p>
                        <p className="text-4xl font-bold text-green-600">{cpk.toFixed(2)}</p>
                        {/* Menggunakan fungsi bantuan baru */}
                        <p className="mt-2">{getCpkSummary(cpk)}</p>
                    </Panel>
                </Col>
            </Row>
             <Row gutter={30} className="mt-4">
                <Col md={6}><Panel><strong>USL:</strong> {usl}</Panel></Col>
                <Col md={6}><Panel><strong>LSL:</strong> {lsl}</Panel></Col>
                <Col md={6}><Panel><strong>Rata-rata (μ):</strong> {mean.toFixed(2)}</Panel></Col>
                <Col md={6}><Panel><strong>Std Dev (σ):</strong> {stdDev.toFixed(3)}</Panel></Col>
            </Row>
            <Panel header={<h4>Catatan Interpretasi</h4>} bordered className="mt-4 bg-gray-50">
                {notes.length > 0 ? (
                    <ul className="list-disc pl-5 space-y-1">
                        {notes.map((note, index) => (
                            <li key={index}>{note}</li>
                        ))}
                    </ul>
                ) : (
                    <p>Proses berjalan dengan baik dan mampu memenuhi spesifikasi.</p>
                )}
            </Panel>
        </Panel>
    );
};


function CpAndCpkCalculationPage() {
    const router = useRouter();
    const { user } = useUser()
    const { id: idPpr } = router.query;
    const { HeaderCell, Cell, Column } = Table;
    const toaster = useToaster();

    const REQUIRED_BATCH_COUNT = 20;

    const [loading, setLoading] = useState(true);
    const [allBatchesForPPR, setAllBatchesForPPR] = useState([]);
    const [selectedBatches, setSelectedBatches] = useState([]);
    const [calculationResult, setCalculationResult] = useState(null);
    const [namaPPR, setNamaPPR] = useState("");
    const [isCalculating, setIsCalculating] = useState(false);
    const resultsRef = useRef(null);
    const chartRef = useRef(null);


    useEffect(() => {
        if (idPpr) {
            const fetchBatches = async () => {
                setLoading(true);
                try {

                    const res = await ApiBeratMSC().getBeratMscByIdPpr({ id_ppr: parseInt(idPpr) });
                    if (res.status === 200 && res.data.length > 0) {
                        setAllBatchesForPPR(res.data);
                        setNamaPPR(res.data[0].nama_ppr);
                    } else {
                        toaster.push(<Notification type="error">Data batch tidak ditemukan untuk PPR ini.</Notification>);
                    }
                } catch (error) {
                    toaster.push(<Notification type="error">Gagal mengambil data batch.</Notification>);
                } finally {
                    setLoading(false);
                }
            };
            fetchBatches();
        }
    }, [idPpr, toaster]);
        useEffect(() => {
        if (calculationResult) {
            resultsRef.current?.scrollIntoView({ behavior: "smooth", block: "start" });
        }
    }, [calculationResult]);

    const handleSelectBatch = (batchData, checked) => {
        const nextSelected = checked
            ? [...selectedBatches, batchData]
            : selectedBatches.filter(item => item.id_berat_msc !== batchData.id_berat_msc);
        
        if (nextSelected.length > REQUIRED_BATCH_COUNT) {
             toaster.push(<Notification type="warning" duration={3000}>{`Hanya bisa memilih ${REQUIRED_BATCH_COUNT} batch.`}</Notification>, { placement: "topCenter" });
             return;
        }

        setSelectedBatches(nextSelected);
    };

    const handleSelectAllOrNone = (value, checked) => {
    if (checked) {
        
        const firstTwentyBatches = allBatchesForPPR.slice(0, REQUIRED_BATCH_COUNT);
        setSelectedBatches(firstTwentyBatches);
        toaster.push(<Notification type="info" duration={3000}>{`${REQUIRED_BATCH_COUNT} batch terpilih.`}</Notification>, { placement: "topCenter" });
    } else {
        setSelectedBatches([]);
    }
};

    const handleCalculate = async () => {
    if (selectedBatches.length !== REQUIRED_BATCH_COUNT) return;

    try {
        setIsCalculating(true);
        setCalculationResult(null);
        resultsRef.current?.scrollIntoView({ behavior: "smooth", block: "start" });
        await new Promise(resolve => setTimeout(resolve, 3000));
        const dataPoints = selectedBatches.map(b => b.berat_msc);
        const { bobot_max: usl, bobot_min: lsl, bobot_std: bobotStd } = selectedBatches[0];
        const sum = dataPoints.reduce((a, b) => a + b, 0);
        const mean = sum / dataPoints.length;
        const stdDev = Math.sqrt(dataPoints.map(x => Math.pow(x - mean, 2)).reduce((a, b) => a + b, 0) / (dataPoints.length - 1));
        const cp = (usl - lsl) / (6 * stdDev);
        const cpu = (usl - mean) / (3 * stdDev);
        const cpl = (mean - lsl) / (3 * stdDev);
        const cpk = Math.min(cpu, cpl);
        setCalculationResult({ cp, cpk, mean, stdDev, usl, lsl, bobotStd });
        toaster.push(<Notification type="success">Perhitungan Cp & Cpk berhasil!</Notification>);

    } catch (error) {
        toaster.push(<Notification type="error">Terjadi kesalahan saat perhitungan.</Notification>);
    } finally {
        setIsCalculating(false);
    }
};

    return (
        <div>
            <Head>
                <title>Kalkulasi Cp & Cpk untuk {namaPPR}</title>
            </Head>
            <ContainerLayout title={`Kalkulasi Cp & Cpk`}>
                <div className="m-4 pt-2">
                    <Stack justifyContent="space-between">
                        <Breadcrumb>
                            <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
                            <Breadcrumb.Item href="/sisko/operator/pmp/list_cp_cpk">List Cp dan Cpk</Breadcrumb.Item>
                            <Breadcrumb.Item active>Kalkulasi</Breadcrumb.Item>
                        </Breadcrumb>
                    </Stack>

                    <Panel  bordered className="mt-4 mb-4">
                        <Stack justifyContent="space-between" alignItems="center" className="mb-3">
                            <Stack.Item>
                                <strong>Produk:</strong> {namaPPR || <Placeholder.Paragraph rows={1} />}
                            </Stack.Item>
                            <Stack.Item>
                                <Stack spacing={6} alignItems="center">
                                    <span
                style={{ cursor: 'pointer', userSelect: 'none' }}
                onClick={() => {
                    const currentlySelected = selectedBatches.length === REQUIRED_BATCH_COUNT;
                    // Memanggil fungsi yang sama dengan checkbox, dengan status kebalikannya
                    handleSelectAllOrNone(null, !currentlySelected);
                }}
            >
                Pilih Semua
            </span>

            <Checkbox
                indeterminate={selectedBatches.length > 0 && selectedBatches.length < REQUIRED_BATCH_COUNT}
                checked={selectedBatches.length === REQUIRED_BATCH_COUNT}
                onChange={handleSelectAllOrNone}
            />

        </Stack>
    </Stack.Item>
                        </Stack>            
                        {loading ? (
                            <Loader center content="Memuat data batch..." />
                        ) : (
                            <Table
                                height={400}
                                data={allBatchesForPPR}
                                bordered
                                cellBordered
                            >
                                <Column width={70} align="center">
                                    <HeaderCell>Pilih</HeaderCell>
                                    <Cell>
                                        {rowData => {
                                            const isChecked = selectedBatches.some(item => item.id_berat_msc === rowData.id_berat_msc);
                                            return (
                                                <Checkbox
                                                    value={rowData.id_berat_msc}
                                                    checked={isChecked}
                                                    onChange={(value, checked) => handleSelectBatch(rowData, checked)}
                                                />
                                            );
                                        }}
                                    </Cell>
                                </Column>
                                <Column width={150} resizable>
                                    <HeaderCell>Kode Batch</HeaderCell>
                                    <Cell dataKey="kode_batch" />
                                </Column>
                                <Column width={150} resizable>
                                    <HeaderCell>Berat (msc)</HeaderCell>
                                    <Cell dataKey="berat_msc" />
                                </Column>
                                
                            </Table>
                        )}
                    </Panel>
                    <Stack justifyContent="end" className="mt-3">
                        <Stack.Item>
                                <Button 
                                    appearance="primary" 
                                    disabled={selectedBatches.length !== REQUIRED_BATCH_COUNT}
                                    onClick={handleCalculate}
                                >
                                    Hitung Cp & Cpk ({selectedBatches.length}/{REQUIRED_BATCH_COUNT})
                                </Button>
                            </Stack.Item>
                    </Stack>
                    

<div ref={resultsRef}>
    {isCalculating && (
        <Panel className="text-center p-5 mt-4">
            <Loader size="lg" content="Sedang menghitung..." vertical />
        </Panel>
    )}

    {!isCalculating && calculationResult && (
        <>
            {/* Panel hasil sekarang menggunakan ref */}
            <div ref={resultsRef}>
                <CalculationResultDisplay result={calculationResult} />
            </div>

            {/* Panel chart sekarang menggunakan ref */}
            <div ref={chartRef}>
                <Panel bordered className="mt-4">
                    <CapabilityChart 
                        selectedBatches={selectedBatches}
                        usl={calculationResult.usl}
                        lsl={calculationResult.lsl}
                        bobotStd={calculationResult.bobotStd}
                    />
                </Panel>
            </div>

            {/* Panggil komponen generator PDF di sini */}
            <Stack justifyContent="center" className="mt-4">
                <CpCpkPDFGenerator
        namaPPR={namaPPR}
        selectedBatches={selectedBatches}
        calculationResult={calculationResult}
        chartRef={chartRef}
        resultsRef={resultsRef}
        username={user?.username}
        noKaryawan={user?.no_karyawan}
    />
            </Stack>
        </>
    )}
</div>                </div>
            </ContainerLayout>
        </div>
    );
}
export default CpAndCpkCalculationPage;