import React from 'react';
import { Line } from 'react-chartjs-2';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    LineElement,
    PointElement,
    Title,
    Tooltip,
    Legend,
} from 'chart.js';

// Daftarkan komponen ChartJS yang dibutuhkan untuk Line Chart
ChartJS.register(
    CategoryScale,
    LinearScale,
    LineElement,
    PointElement,
    Title,
    Tooltip,
    Legend
);

const CapabilityChart = ({ selectedBatches, usl, lsl, bobotStd  }) => {
    // 1. Siapkan data untuk chart
    const labels = selectedBatches.map(batch => batch.kode_batch); // Sumbu X diisi kode batch
    const actualData = selectedBatches.map(batch => batch.berat_msc);

    const chartData = {
        labels,
        datasets: [
            {
                label: 'Berat (msc)',
                data: actualData,
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                tension: 0.1, // Membuat garis sedikit melengkung
            },
            {
                label: 'USL (Batas Atas)',
                data: Array(labels.length).fill(usl), // Buat array berisi nilai USL
                borderColor: 'rgb(255, 99, 132)',
                borderDash: [5, 5], // Buat garis putus-putus
                pointRadius: 0, // Hilangkan titik pada garis batas
            },
            {
                label: 'LSL (Batas Bawah)',
                data: Array(labels.length).fill(lsl), // Buat array berisi nilai LSL
                borderColor: 'rgb(255, 99, 132)',
                borderDash: [5, 5], // Buat garis putus-putus
                pointRadius: 0, // Hilangkan titik pada garis batas
            },
            {
            label: 'Bobot Standar',
            data: Array(labels.length).fill(bobotStd),
            borderColor: 'rgb(75, 192, 192)', // Warna hijau/teal
            borderWidth: 2,
            pointRadius: 0,
        },
        ],
    };

    // 2. Konfigurasi opsi untuk chart
    const chartOptions = {
        responsive: true,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: 'Grafik Tren Berat (msc) per Batch',
                font: { size: 16 },
            },
            tooltip: {
                mode: 'index',
                intersect: false,
            },
        },
        scales: {
            y: {
                title: {
                    display: true,
                    text: 'Berat (msc)',
                },
                // Beri sedikit ruang di atas dan bawah garis batas
                min: lsl - 2,
                max: usl + 2,
            },
            x: {
                 title: {
                    display: true,
                    text: 'Kode Batch',
                },
            }
        },
    };

    return <Line options={chartOptions} data={chartData} />;
};

export default CapabilityChart;