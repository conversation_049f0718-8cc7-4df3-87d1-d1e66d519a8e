import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import Head from "next/head";
import ContainerLayout from "@/components/layout/ContainerLayout";
import ApiSiskoTransactionHeader from "@/pages/api/sisko/transaction_h/api_sisko_transaction_h";
import ApiBeratMSC from "@/pages/api/sisko/berat_msc/api_berat_msc";
import { Panel, Stack, Breadcrumb, Loader, Table, Button, Input, InputGroup, Pagination, useToaster, Notification, Whisper, Tooltip } from "rsuite";
import { useUser } from "@/context/UserContext";
import withRoleAccess from '@/components/auth/withRoleAccess';
import rolePermissions from '@/utils/rolePermissions';
import SearchIcon from '@rsuite/icons/Search';
import EditIcon from '@rsuite/icons/Edit';
import PlusRoundIcon from '@rsuite/icons/PlusRound';
import VisibleIcon from '@rsuite/icons/Visible';
import UnvisibleIcon from '@rsuite/icons/Unvisible';

function SiskoFinishing() {
    const router = useRouter();
    const { HeaderCell, Cell, Column } = Table;
    const [loading, setLoading] = useState(true);
    const [transactionHeadersDataState, setTransactionHeadersDataState] = useState([]);
    const [MSCDataState, setMSCDataState] = useState([]);
    const { user, isAuthenticated, loading: userLoading } = useUser();
    const [sessionAuth, setSessionAuth] = useState(null);
    const [moduleName, setModuleName] = useState("");
    const toaster = useToaster();


    const [page, setPage] = useState(1);
    const [limit, setLimit] = useState(10);
    const [sortColumn, setSortColumn] = useState();
    const [sortType, setSortType] = useState();
    const [searchKeyword, setSearchKeyword] = useState('');


    const formatDate = (dateString) => {
        if (!dateString) return "-";
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return "-";

        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    };


    const handleSearchChange = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };


    const filteredData = searchKeyword
        ? transactionHeadersDataState.filter((item) => {
            const keyword = searchKeyword.toLowerCase();
            return (
                (item.nama_ppr && item.nama_ppr.toLowerCase().includes(keyword)) ||
                (item.kode_batch && item.kode_batch.toLowerCase().includes(keyword)) ||
                (item.dibuat_oleh && item.dibuat_oleh.toLowerCase().includes(keyword)) ||
                (item.disetujui_oleh && item.disetujui_oleh.toLowerCase().includes(keyword))
            );
        })
        : transactionHeadersDataState;


    const handleSortColumn = (sortColumn, sortType) => {
        setSortColumn(sortColumn);
        setSortType(sortType);
    };



    const getFilteredData = () => {

        const getPriorityScore = (transactionRow) => {
            const mscData = MSCDataState.find(msc => msc.id_transaksi_header === transactionRow.id_transaksi_header);


            if (!mscData) {
                return 1;
            }


            switch (mscData.status_transaksi) {

                case 0:
                    return 2;

                case 1:
                    return 4;

                case 2:
                    return 3;

                default:
                    return 5;
            }
        };


        const dataToSort = [...filteredData];

        dataToSort.sort((a, b) => {

            const priorityA = getPriorityScore(a);
            const priorityB = getPriorityScore(b);


            if (priorityA !== priorityB) {
                return priorityA - priorityB;
            }


            if (sortColumn && sortType) {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.toLowerCase();
                }
                if (typeof y === "string") {
                    y = y.toLowerCase();
                }
                if (sortType === "asc") {
                    return x > y ? 1 : -1;
                } else {
                    return x < y ? 1 : -1;
                }
            }


            return 0;
        });

        return dataToSort;
    };


    const getPaginatedData = (data, limit, page) => {
        const start = (page - 1) * limit;
        const end = start + limit;
        return data.slice(start, end);
    };

    const totalRowCount = searchKeyword ? filteredData.length : transactionHeadersDataState.length;


    const viewHandler = async (idTransHeader) => {
        const url = `${process.env.NEXT_PUBLIC_PIMS_FE}/sisko/operator/reporting/pdf?idTransHeader=${parseInt(idTransHeader)}`;
        window.open(url, "_blank");
    };


    const showNotification = (type, message) => {
        if (type === "success") {
            toaster.push(
                <Notification type="success" header="Success">
                    {message}
                </Notification>,
                { placement: "topEnd" }
            );
        } else if (type === "error") {
            toaster.push(
                <Notification type="error" header="Error">
                    {message}
                </Notification>,
                { placement: "topEnd" }
            );
        }
    };


    useEffect(() => {
        if (userLoading) return;
        if (!isAuthenticated()) {
            router.push("/login");
            return;
        }
        setModuleName(user.module_name || "");
        setSessionAuth(user);
        HandleGetAllFullyApproveTransactionHeaderApi();
        HandleGetAllBeratMsc();
    }, [user, userLoading]);


    const HandleGetAllFullyApproveTransactionHeaderApi = async () => {
        try {
            setLoading(true);
            const res = await ApiSiskoTransactionHeader().getAllFullyApproveSiskoTransactionHeader();

            console.log("res", res);
            if (res.status === 200) {
                setTransactionHeadersDataState(res.data || []);
            } else {
                console.log("error on Get All Api ", res.message);
                showNotification("error", "Gagal mengambil data transaksi");
            }
        } catch (error) {
            console.log("error on catch Get All Api", error);
            showNotification("error", "Terjadi kesalahan saat mengambil data");
        } finally {
            setLoading(false);
        }
    };

    const HandleGetAllBeratMsc = async () => {
        try {
            setLoading(true);
            const res = await ApiBeratMSC().getAllBeratMSC();

            console.log("res", res);
            if (res.status === 200) {
                setMSCDataState(res.data || []);
            } else {
                console.log("error on Get All Api ", res.message);
                showNotification("error", "Gagal mengambil data transaksi");
            }
        } catch (error) {
            console.log("error on catch Get All Api", error);
            showNotification("error", "Terjadi kesalahan saat mengambil data");
        } finally {
            setLoading(false);
        }
    };



    return (
        <div>
            <Head>
                <title>Laporan Transaksi Disetujui</title>
            </Head>
            <ContainerLayout title="Halaman Transaksi Disetujui">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <Breadcrumb>
                                <Breadcrumb.Item href="/dashboard">Dashboard</Breadcrumb.Item>
                                <Breadcrumb.Item href="/sisko/operator/creation">Sisko</Breadcrumb.Item>
                                <Breadcrumb.Item active>Finishing Transaksi</Breadcrumb.Item>
                            </Breadcrumb>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Daftar Penghitungan Nilai</h5>
                            </Stack>
                        }
                    ></Panel>

                    <Panel bordered>
                        <Stack spacing={10} justifyContent="space-between" className="mb-3">

                            <InputGroup inside style={{ width: 300 }}>
                                <Input
                                    placeholder="Cari..."
                                    value={searchKeyword}
                                    onChange={handleSearchChange}
                                />
                                <InputGroup.Addon>
                                    <SearchIcon />
                                </InputGroup.Addon>
                            </InputGroup>
                        </Stack>

                        {loading ? (
                            <Loader center content="Memuat data..." />
                        ) : (
                            <>
                                <Table
                                    data={getPaginatedData(getFilteredData(), limit, page)}
                                    bordered
                                    height={400}
                                    cellBordered
                                    sortColumn={sortColumn}
                                    sortType={sortType}
                                    onSortColumn={handleSortColumn}
                                >
                                    <Table.Column width={70} align="center" fixed>
                                        <Table.HeaderCell>No</Table.HeaderCell>
                                        <Table.Cell>
                                            {(rowData, rowIndex) => {
                                                return (rowIndex + 1) + (page - 1) * limit;
                                            }}
                                        </Table.Cell>
                                    </Table.Column>

                                    <Table.Column width={70} sortable resizable>
                                        <Table.HeaderCell>ID Transaksi</Table.HeaderCell>
                                        <Table.Cell dataKey="id_transaksi_header" />
                                    </Table.Column>

                                    <Table.Column width={200} sortable resizable>
                                        <Table.HeaderCell>Nama PPR</Table.HeaderCell>
                                        <Table.Cell dataKey="nama_ppr" />
                                    </Table.Column>

                                    <Table.Column width={150} sortable resizable>
                                        <Table.HeaderCell>Kode Batch</Table.HeaderCell>
                                        <Table.Cell dataKey="kode_batch" />
                                    </Table.Column>

                                    <Table.Column width={150} sortable resizable>
                                        <Table.HeaderCell>Tanggal Dibuat</Table.HeaderCell>
                                        <Table.Cell>
                                            {(rowData) => formatDate(rowData.tanggal_dibuat)}
                                        </Table.Cell>
                                    </Table.Column>

                                    <Table.Column width={200} sortable resizable>
                                        <Table.HeaderCell>Dibuat Oleh</Table.HeaderCell>
                                        <Table.Cell dataKey="dibuat_oleh" />
                                    </Table.Column>

                                    <Table.Column width={150} sortable resizable>
                                        <Table.HeaderCell>Tanggal Disetujui</Table.HeaderCell>
                                        <Table.Cell>
                                            {(rowData) => formatDate(rowData.disetujui_tanggal)}
                                        </Table.Cell>
                                    </Table.Column>

                                    <Table.Column width={200} sortable resizable>
                                        <Table.HeaderCell>Disetujui Oleh</Table.HeaderCell>
                                        <Table.Cell dataKey="disetujui_oleh" />
                                    </Table.Column>

                                    <Column width={160} fixed="right" align="center">
                                        <HeaderCell>Massa Siap Cetak</HeaderCell>
                                        <Cell style={{ padding: "8px" }}>
                                            {(rowData) => {

                                                const mscDataForRow = MSCDataState.find(msc => msc.id_transaksi_header === rowData.id_transaksi_header);

                                                return (
                                                    <div>
                                                        <Button
                                                            appearance="subtle"
                                                            disabled={mscDataForRow && mscDataForRow.status_transaksi === 1}
                                                            onClick={() => {
                                                                const idHeader = rowData.id_transaksi_header;

                                                                if (mscDataForRow) {
                                                                    router.push(`/sisko/operator/finishing/msc?IdHeader=${idHeader}&mode=edit`);
                                                                } else {
                                                                    router.push(`/sisko/operator/finishing/msc?IdHeader=${idHeader}&mode=add`);
                                                                }
                                                            }}
                                                        >
                                                            {mscDataForRow ? (
                                                                <Whisper placement="top" trigger="hover" speaker={<Tooltip>Edit MSC</Tooltip>}>
                                                                    {/* 3. Atur warna berdasarkan status_transaksi dari mscDataForRow */}
                                                                    <EditIcon style={{ color: mscDataForRow.status_transaksi === 0 ? 'red' : undefined }} />
                                                                </Whisper>
                                                            ) : (
                                                                <Whisper placement="top" trigger="hover" speaker={<Tooltip>Tambah MSC</Tooltip>}>
                                                                    <PlusRoundIcon />
                                                                </Whisper>
                                                            )}
                                                        </Button>
                                                        <Button
                                                            appearance="subtle"
                                                            onClick={() => {
                                                                const idHeader = rowData.id_transaksi_header;
                                                                router.push(`/sisko/operator/finishing/msc?IdHeader=${idHeader}&mode=view`);
                                                            }}

                                                            disabled={!mscDataForRow}
                                                        >
                                                            <Whisper placement="top" trigger="hover" speaker={<Tooltip>Lihat MSC</Tooltip>}>
                                                                {mscDataForRow ? (
                                                                    <VisibleIcon style={{ fontSize: "16px", color: "#1675e0" }} />
                                                                ) : (
                                                                    <UnvisibleIcon style={{ fontSize: "16px", color: "#cccccc" }} />
                                                                )}
                                                            </Whisper>
                                                        </Button>
                                                    </div>
                                                );
                                            }}
                                        </Cell>
                                    </Column>

                                </Table>

                                <div style={{ padding: 20 }}>
                                    <Pagination
                                        prev
                                        next
                                        first
                                        last
                                        ellipsis
                                        boundaryLinks
                                        maxButtons={5}
                                        size="xs"
                                        layout={["total", "-", "limit", "|", "pager", "skip"]}
                                        limitOptions={[10, 30, 50]}
                                        total={totalRowCount}
                                        limit={limit}
                                        activePage={page}
                                        onChangePage={setPage}
                                        onChangeLimit={(limit) => {
                                            setPage(1);
                                            setLimit(limit);
                                        }}
                                    />
                                </div>
                            </>
                        )}
                    </Panel>
                </div>
            </ContainerLayout>
        </div>
    );
}

export default withRoleAccess(SiskoFinishing, rolePermissions['sisko/operator/finishing']);