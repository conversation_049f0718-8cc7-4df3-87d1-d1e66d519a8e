import { useEffect, useState } from "react";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import { Button } from "rsuite";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFileDownload } from "@fortawesome/free-solid-svg-icons";

pdfMake.vfs = pdfFonts.pdfMake.vfs;

const LineClearPdfComponent = ({ LineClearanceData, sessionAuth }) => {
  const generatePdfNew = (action) => {
    if (!LineClearanceData) {
      console.error("Data belum tersedia", LineClearanceData);
      return;
    }

    const data = LineClearanceData;

    // Format tanggal menggunakan singkatan bulan
    let monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Format Date Checked (Created_Date)
    const datePart = data.create_date.split("T")[0];
    let [day, month, year] = datePart.split("-");
    let monthAbbreviation = monthNames[parseInt(month, 10) - 1];
    let formattedDateCreated = `${day}-${monthAbbreviation}-${year}`;

    const datePartApproved = data.approval_date.split("T")[0];
    let [dayApproved, monthApproved, yearApproved] =
      datePartApproved.split("-");
    let monthAbbreviationApproved = monthNames[parseInt(monthApproved, 10) - 1];
    let formattedDateApproved = `${dayApproved}-${monthAbbreviationApproved}-${yearApproved}`;

    const operatedByText = `Provided By: ${sessionAuth.employee_id} - ${sessionAuth.employee_name} ${formattedDateCreated}`;
    // const sopText =
    //   "This document is printed by refer to SOP Document no SOP-PR-O061.00";

    const headerContent = [
      {
        text: "Line Clearance - Granulasi Report ",
        style: "header",
      },
      {
        text: `Product Code           : ${data.product_code}`,
        style: "detail",
      },
      {
        text: `Batch Code               : ${data.batch_code}`,
        style: "detail",
      },
      {
        text: `Dibuat Oleh               : ${data.create_by} - ${formattedDateCreated}`,
        style: "detail",
      },
      {
        text: `Tanggal Disetujui     : ${
          data.approval_by && formattedDateApproved
            ? `${data.approval_by} - ${formattedDateApproved}`
            : "-"
        }`,
        style: "detail",
      },
      { text: "", margin: [0, 0, 0, 10] },
    ];

    const tableBody = [["No", "Parameter", "Nilai"]];
    const renderYesNo = (value) => {
      if (value === 1) {
        return "Y";
      } else if (value === 0) {
        return "N/A";
      } else {
        return "-";
      }
    };

    tableBody.push([
      1,
      [
        "Pembersihan Rutin, Boleh ada sisa produk jika:",
        {
          ul: [
            "a. Ganti batch produk",
            "b. Formula sama tetapi kode produk berbeda.",
          ],
        },
      ],
      renderYesNo(data.clean_routine),
    ]);
    tableBody.push([
      2,
      " Pastikan Kabel kalitrasi dan kualifikasi pada mesin masih berlaku",
      renderYesNo(data.calibration_routine),
    ]);
    tableBody.push([
      3,
      "Pastikan kebersihan lantai dan area mesin High Shear Mixer Granulator GEA \n (PMA 800) (M01GEAS0001) serta lemari/ meja.\nPastikan tidak ada dokumen, prodak antara dan bahan / material, dari produk sebelumnya.",
      renderYesNo(data.area_routine),
    ]);
    tableBody.push([
      4,
      'Pastikan tidak ada label identitas mesin "SEDANG PROSES" dari produk sebelumnya yang tertinggal di ruangan.',
      renderYesNo(data.label_routine),
    ]);
    tableBody.push([
      5,
      [
        "Pembersihan Berkala, Jila:",
        {
          ul: [
            "a. Ganti produk",
            "b. Ganti batch tetapi sudah mencapai batas campaign batch (sesuai Supporting Document).",
            " c. Mesin tidak digunakan lebih dari hari yang sudah ditetapkan (sesuai Supporting Document pembersihan tiap mesin/ alat).",
          ],
        },
      ],
      renderYesNo(data.clean_periodic),
    ]);
    tableBody.push([
      6,
      " Pastikan label kalitrasi dan kualifikasi pada mesin masih berlaku.",
      renderYesNo(data.calibration_periodic),
    ]);
    tableBody.push([
      7,
      "Pastikan kebersihan lantai dan area mesin High Shear Mixer Granulator GEA\nPMA 800 (M0IGEA8000I) serta lemari / meja.\nPastikan tidak ada dokumen, produk antara dan bahan /material, dari produk sebelumnya.",
      renderYesNo(data.area_periodic),
    ]);
    tableBody.push([
      8,
      'Pastikan label "SIAP PAKAI" tersedia dan masih berlaku.',
      renderYesNo(data.label_preiodic),
    ]);
    tableBody.push([
      9,
      "Pastikan dust collector dan filter low return bersih dan terbebas dari debu serta dalam kondisi yang baik.",
      renderYesNo(data.dust_periodic),
    ]);
    tableBody.push([
      10,
      [
        "Pastikan kebersihan area mesin High Shear Mixer Granulator GEA PMA 800 (M0IGEA8000I) sesuai area part dibawah ini:",
        {
          ul: [
            "a. Tidak terdapat sisa produk dari produk sebelumnya.",
            "Tidak terdapat debu dan kotoran serta baik untuk digunakan.",
          ],
        },
        "Discharge",
      ],
      renderYesNo(data.discharge),
    ]);
    tableBody.push([
      11,
      "Container / bowl mesin",
      renderYesNo(data.bowl_machine),
    ]);
    tableBody.push([12, "Tutup container", renderYesNo(data.lid_container)]);
    tableBody.push([
      13,
      "Product container",
      renderYesNo(data.product_container),
    ]);
    tableBody.push([14, "Lantai panggung", renderYesNo(data.floor)]);
    tableBody.push([
      15,
      "Filter dan rangka filter pastikan Juga kekuatan dan kekencangan pemasangan klem",
      renderYesNo(data.filter_clem),
    ]);
    tableBody.push([16, "Panel mesin", renderYesNo(data.machine_panel)]);
    tableBody.push([17, "Spray Gun Binder", renderYesNo(data.spray_gun)]);
    tableBody.push([
      18,
      "Impeller dan chopper",
      renderYesNo(data.impeller_chopper),
    ]);
    tableBody.push([
      19,
      [
        "Pastikan kebersihan area mesin High Shear Mixer Granulator GEA PMA 800 (M0IGEA8000I) sesuai area / part dibawah ini:",
        {
          ul: [
            "a. Tidak terdapat sisa produk dari produk sebelumnya.",
            "Tidak terdapat debu dan kotoran serta baik untuk digunakan.",
          ],
        },
        "Konektor Granulator ke Wet mill",
      ],
      renderYesNo(data.granulator_wetmill),
    ]);
    tableBody.push([
      20,
      " Konektor antara selang transfer dan ayakan (jika digunakan)",
      renderYesNo(data.connector_sifting),
    ]);
    tableBody.push([
      21,
      "Pisau ayak/impeller",
      renderYesNo(data.impeller_sifting),
    ]);
    tableBody.push([22, "Mesh", renderYesNo(data.mesh)]);
    tableBody.push([
      23,
      "Seal pada mesin ayak",
      renderYesNo(data.seal_sifting),
    ]);
    tableBody.push([
      24,
      "Konektor antara ayakan dan FBD",
      renderYesNo(data.fbd_sifting),
    ]);
    tableBody.push([
      25,
      'Pastikan tidak ada label identitas mesin "SEDANG PROSES "dari produk sebelumnya yang tertinggal di Arca High Shear Mixer Granalator GEA PMA 800 (MOIGEA80001).',
      renderYesNo(data.label_process),
    ]);
    // Definisikan objek PDF (document definition)
    var dd = {
      content: [
        ...headerContent,
        {
          style: "tableExample",
          table: {
            widths: ["auto", "auto", "auto"],
            body: tableBody,
          },
        },
      ],
      styles: {
        header: {
          fontSize: 22,
          bold: true,
          alignment: "center",
          margin: [0, 5, 0, 15],
        },
        tableExample: {
          margin: [0, 5, 0, 25],
          alignment: "justify",
        },
      },
      footer: (currentPage, pageCount) => {
        return {
          columns: [
            {
              width: "*",
              text: operatedByText + "\n" + "\n",
              margin: [40, 0, 0, 0],
            },
            {
              width: "auto",
              text: "Page " + currentPage + " of " + pageCount,
              alignment: "right",
              margin: [0, 0, 40, 0],
            },
          ],
        };
      },
      pageMargins: [40, 60, 40, 60],
    };

    if (action === "Download") {
      pdfMake
        .createPdf(dd)
        .download(`Print Line Clearance - Granulasi Report.pdf`);
    } else {
      pdfMake.createPdf(dd).open();
    }
  };

  return (
    <div>
      <Button
        onClick={() => generatePdfNew("preview")}
        style={{ marginRight: "5px" }}>
        Preview
      </Button>
      <Button onClick={() => generatePdfNew("Download")}>
        <FontAwesomeIcon icon={faFileDownload} style={{ fontSize: 15 }} />{" "}
        Download
      </Button>
    </div>
  );
};

export default LineClearPdfComponent;
