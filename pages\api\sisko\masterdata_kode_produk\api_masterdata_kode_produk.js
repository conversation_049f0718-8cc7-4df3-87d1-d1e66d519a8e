import createApiFunction from "@/lib/apiClient";

export default function ApiMasterdataKodeProduk() {
  return {
    getAllMasterKodeProduk: createApiFunction("get", "sisko/masterdata_kode_produk/list"),
    getAllActiveMasterKodeProduk: createApiFunction("get", "sisko/masterdata_kode_produk/list-active"),
    getMasterKodeProdukById: createApiFunction("post", "sisko/masterdata_kode_produk/id"),
    createMasterKodeProduk: createApiFunction("post", "sisko/masterdata_kode_produk/create"),
    editMasterKodeProduk: createApiFunction("put", "sisko/masterdata_kode_produk/edit"),
    editStatusMasterKodeProduk: createApiFunction("put", "sisko/masterdata_kode_produk/edit-status"),
  };
}
