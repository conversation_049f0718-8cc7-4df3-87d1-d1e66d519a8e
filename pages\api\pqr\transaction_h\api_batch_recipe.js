import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_PIMSORA_SERVICE}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiTransactionHeader() {
  return {
    getAllBatchCode: createApiFunction("get", "pqr/batch/list"),
    getRecipeByBatchCode: createApiFunction("post", "pqr/erelease/list-recipe"),
  };
}
