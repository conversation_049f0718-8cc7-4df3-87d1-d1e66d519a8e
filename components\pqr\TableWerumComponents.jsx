import { Mo<PERSON>, Table, Loader } from "rsuite";
import { useEffect, useState } from "react";
import APIpqrwerumoc from "@/pages/api/pqr/oc_werum/api_oc_werum";

const TableWerumComponents = ({ batchCode }) => {
  const { HeaderCell, Cell, Column } = Table;
  const [sortColumn, setSortColumn] = useState(null);
  const [sortType, setSortType] = useState("asc");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [werumDataState, setWerumDataState] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch data Werum berdasarkan batchCode
  const getAllWerumPQR = async (batchNumber) => {
    if (batchNumber) {
      setLoading(true);
      try {
        const res = await APIpqrwerumoc().getOcListWerumPqr({ batch_number: batchNumber });
        if (res.status === 200) {
          setWerumDataState(res.data);
        } else {
          console.log("Error fetching Werum data:", res.message);
        }
      } catch (error) {
        console.log("Error fetching Werum data:", error.message);
      } finally {
        setLoading(false);
      }
    } else {
      setWerumDataState([]); // Reset data jika batchNumber kosong
      setLoading(false); // Pastikan loading juga direset
    }
  };


  useEffect(() => {
    if (batchCode) {
      getAllWerumPQR(batchCode);
    }
  }, [batchCode]);

  const handleSortColumn = (columnKey) => {
    if (sortColumn === columnKey) {
      setSortType(sortType === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(columnKey);
      setSortType("asc");
    }
  };

  const getFilteredData = () => {
    let filteredData = [...werumDataState];

    if (sortColumn && sortType) {
      filteredData = filteredData.sort((a, b) => {
        let x = a[sortColumn];
        let y = b[sortColumn];

        if (typeof x === "string" && typeof y === "string") {
          x = x.toLowerCase();
          y = y.toLowerCase();
        }

        if (x < y) {
          return sortType === "asc" ? -1 : 1;
        }
        if (x > y) {
          return sortType === "asc" ? 1 : -1;
        }
        return 0;
      });
    }

    return filteredData;
  };

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };
  return (
  <div>
        {loading ? (
          <div style={{ display: "flex", justifyContent: "center", alignItems: "center", height: "400px" }}>
            <Loader size="md" content="Loading..." />
          </div>
        ) : (
          <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn} autoHeight>
            <Column width={180} align="center" sortable fullText>
              <HeaderCell>Produk</HeaderCell>
              <Cell dataKey="PRODUK" />
            </Column>
            <Column width={200} align="center" sortable fullText>
              <HeaderCell>Produk Name</HeaderCell>
              <Cell dataKey="PRODUK_NAME" />
            </Column>
            <Column width={150} align="center" sortable fullText>
              <HeaderCell>Step</HeaderCell>
              <Cell dataKey="STEP" />
            </Column>
            <Column width={150} align="center" sortable fullText>
              <HeaderCell>BN</HeaderCell>
              <Cell dataKey="BN" />
            </Column>
            <Column width={180} align="center" sortable fullText>
              <HeaderCell>Schedule</HeaderCell>
              <Cell dataKey="SCHEDULE" />
            </Column>
            <Column width={180} align="center" sortable fullText>
              <HeaderCell>Material</HeaderCell>
              <Cell dataKey="MATERIAL" />
            </Column>
            <Column width={200} align="center" sortable fullText>
              <HeaderCell>Material Name</HeaderCell>
              <Cell dataKey="MATERIAL_NAME" />
            </Column>
            <Column width={150} align="center" sortable fullText>
              <HeaderCell>Lot QA</HeaderCell>
              <Cell dataKey="LOT_QA" />
            </Column>
            <Column width={150} align="center" sortable fullText>
              <HeaderCell>Qty</HeaderCell>
              <Cell dataKey="QTY" />
            </Column>
            <Column width={120} align="center" sortable fullText>
              <HeaderCell>UOM</HeaderCell>
              <Cell dataKey="UOM" />
            </Column>
            <Column width={180} align="center" sortable fullText>
              <HeaderCell>Tanggal</HeaderCell>
              <Cell dataKey="TANGGAL" />
            </Column>
            <Column width={180} align="center" sortable fullText>
              <HeaderCell>Weigher</HeaderCell>
              <Cell dataKey="WEIGHER" />
            </Column>
            <Column width={180} align="center" sortable fullText>
              <HeaderCell>Timbangan</HeaderCell>
              <Cell dataKey="TIMBANGAN" />
            </Column>
            <Column width={200} align="center" sortable fullText>
              <HeaderCell>Create By</HeaderCell>
              <Cell dataKey="CREATE_BY" />
            </Column>
            <Column width={200} align="center" sortable fullText>
              <HeaderCell>Date TMB</HeaderCell>
              <Cell dataKey="DATE_TMB" />
            </Column>
            <Column width={200} align="center" sortable fullText>
              <HeaderCell>Manufaktur Name</HeaderCell>
              <Cell dataKey="MANUFAKTUR_NAME" />
            </Column>
            <Column width={200} align="center" sortable fullText>
              <HeaderCell>Expiration Date</HeaderCell>
              <Cell dataKey="EXPIRATION_DATE" />
            </Column>
          </Table>
        )}
       </div>
  );
};

export default TableWerumComponents;
