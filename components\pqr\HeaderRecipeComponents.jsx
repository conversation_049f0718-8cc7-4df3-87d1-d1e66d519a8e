import React, { useState, useEffect } from "react";
import { Panel, Form, Grid, Row, Col, Loader, PanelGroup } from "rsuite";
import ApiTransRecipeH from "@/pages/api/pqr/supervisor/api_transaction_recipe";

export default function HeaderRecipeComponents({ transactionRecipeHeaderId }) {
  const [transRecipeData, setTransRecipeData] = useState(null);
  const [error, setError] = useState(null);

  const HandleGetTransRecipeH = async (id_trans_header) => {
    try {
      const res = await ApiTransRecipeH().GetTransactionRecipeByIdTransHeader({ id_trans_header });

      if (res.status === 200) {
        const data = res.data || {};
        const details = Array.isArray(data.detail) ? data.detail : [];

        const processedData = {
          ...data,
          komposisi: {
            detail: details.filter((item) => item.column_name === "KOMPOSISI").map((item) => item.detail),
          },
          komposisi_tambahan: {
            detail: details.filter((item) => item.column_name === "KOMPOSISI_TAMBAHAN").map((item) => item.detail),
          },
          summary_test2: {
            detail: details.filter((item) => item.column_name === "SUMMARY_TEST2").map((item) => item.detail),
          },
          packaging: {
            detail: details.filter((item) => item.column_name === "PACKAGING").map((item) => item.detail),
          },
        };

        setTransRecipeData(processedData);
      } else {
        console.error("Error fetching data:", res.message);
        setError(res.message || "Unknown error occurred");
      }
    } catch (error) {
      console.error("Error on catch:", error);
      setError("Error fetching data");
    }
  };

  useEffect(() => {
    if (!transactionRecipeHeaderId) return;
    console.log("Fetching data for id_trans_header:", transactionRecipeHeaderId);
    HandleGetTransRecipeH(transactionRecipeHeaderId);
  }, [transactionRecipeHeaderId]);

  if (error) {
    return <div>No recipe data saved for this transaction please wait until supervisor approve</div>;
  }

  if (!transRecipeData) {
    return (
      <div style={{ textAlign: "center", padding: "2rem" }}>
        <Loader size="md" content="Loading..." vertical />
      </div>
    );
  }

  // Buat fungsi helper untuk memformat tanggal
  const formatDate = (dateValue) => {
    if (!dateValue) return "";

    const date = new Date(dateValue);

    if (isNaN(date.getTime())) return dateValue;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  };

  return (
    <>
      <Panel header="Data Transaction Recipe" bordered className="mb-3">
        <Form layout="vertical">
          <div className="container-fluid">
            <div className="row">
              <div className="col-md-6">
                <table className="table table-bordered">
                  <tbody>
                    {[
                      { label: "ID Recipe", name: "id_recipe" },
                      { label: "ID Trans Header", name: "id_trans_header" },
                      { label: "Batch No", name: "batch_no" },
                      { label: "Recipe No", name: "recipe_no" },
                      { label: "Formula ID", name: "formula_id" },
                      { label: "Lot Number", name: "lot_number" },
                      { label: "Status ID", name: "status_id" },
                      { label: "Routing ID", name: "routing_id" },
                      { label: "Recipe No 1", name: "recipe_no_1" },
                      { label: "Type PPI", name: "type_ppi" },
                      { label: "Recipe ID", name: "recipe_id" },
                      { label: "Recipe Description", name: "recipe_description" },
                      { label: "Spec ID", name: "spec_id" },
                      { label: "Inventory Item ID", name: "inventory_item_id" },
                      { label: "Count Batch", name: "count_batch" },
                      { label: "Metode Analisa", name: "metode_analisa" },
                      { label: "Batch Size", name: "batch_size" },
                    ].map(({ label, name, format }, index) => (
                      <tr key={name}>
                        <td>{index + 1}</td>
                        <td style={{ display: "flex", justifyContent: "start", gap: "8px" }}>
                          <strong>{label}</strong>
                          <span>:</span>
                          <span>{format ? format(transRecipeData[name]) : transRecipeData[name] || "-"}</span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="col-md-6">
                <table className="table table-bordered">
                  <tbody>
                    {[
                      { label: "Package Batch No", name: "package_batch_no" },
                      { label: "Package Formula ID", name: "package_formula_id" },
                      { label: "Package Parent Lot Number", name: "package_parent_lot_number" },
                      { label: "Package Routing ID", name: "package_routing_id" },
                      { label: "Package Type PPI", name: "package_type_ppi" },
                      { label: "Package Recipe2", name: "package_recipe2" },
                      { label: "Package Recipe Description", name: "package_recipe_description" },
                      { label: "Package Shelf Life Days", name: "package_shelf_life_days" },
                      { label: "Package NIE2", name: "package_nie2" },
                      { label: "Created Date", name: "created_date", format: (val) => formatDate(val) },
                      { label: "Created By", name: "created_by" },
                      { label: "Updated Date", name: "updated_date", format: (val) => formatDate(val) },
                      { label: "Updated By", name: "updated_by" },
                      { label: "Deleted Date", name: "deleted_date", format: (val) => formatDate(val) },
                      { label: "Deleted By", name: "deleted_by" },
                      { label: "Is Active", name: "is_active", format: (val) => (val ? "Aktif" : "Tidak Aktif") },
                    ].map(({ label, name, format }, index) => (
                      <tr key={name}>
                        <td>{index + 18}</td>
                        <td style={{ display: "flex", justifyContent: "start", gap: "8px" }}>
                          <strong>{label}</strong>
                          <span>:</span>
                          <span>{format ? format(transRecipeData[name]) : transRecipeData[name] || "-"}</span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </Form>
      </Panel>

      <Panel header="Data Detail" bordered className="mb-3">
        <PanelGroup>
          <Panel header="Komposisi" bordered>
            <ul>{transRecipeData.komposisi?.detail?.map((item, index) => <li key={index}>{`${index + 1}. ${item}`}</li>) || "-"}</ul>
          </Panel>
          <Panel header="Komposisi Tambahan" bordered>
            <ul>{transRecipeData.komposisi_tambahan?.detail?.map((item, index) => <li key={index}>{`${index + 1}. ${item}`}</li>) || "-"}</ul>
          </Panel>
          <Panel header="Summary Test2" bordered>
            <ul>{transRecipeData.summary_test2?.detail?.map((item, index) => <li key={index}>{`${index + 1}. ${item}`}</li>) || "-"}</ul>
          </Panel>
          <Panel header="Packaging" bordered>
            <ul>{transRecipeData.packaging?.detail?.map((item, index) => <li key={index}>{`${index + 1}. ${item}`}</li>) || "-"}</ul>
          </Panel>
        </PanelGroup>
      </Panel>
    </>
  );
}
